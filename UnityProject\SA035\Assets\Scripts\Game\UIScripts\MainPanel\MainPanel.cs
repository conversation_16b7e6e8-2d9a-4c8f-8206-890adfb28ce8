//Panel模版
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Qarth;
using System;
using TMPro;
using DG.Tweening;



namespace GameWish.Game
{

    public class MainPanel : ScrollAnimPanel
    {
        [SerializeField] private Button m_BtnStart;
        [SerializeField] private Button m_BtnLeft;
        [SerializeField] private Button m_BtnRight;
        [SerializeField] private Button m_BtnInfo;
        [SerializeField] private Button m_BtnOutpost;
        [SerializeField] private Button m_BtnBoxReward;
        [SerializeField] private Button m_BtnTarot;
        [SerializeField] private Transform m_TarotEffectRoot;
        [SerializeField] private Button m_BtnSetting;
        [SerializeField] private Button m_BtnAdPack;
        [SerializeField] private Button m_BtnNewbiePack;
        [SerializeField] private Button m_BtnFirstPurchasePack;
        [SerializeField] private But<PERSON> m_BtnPassport;
        [SerializeField] private TextMeshProUGUI m_TxtPassportLevel;
        [SerializeField] private TextMeshProUGUI m_TxtPassportProgress;
        [SerializeField] private Slider m_SldPassportProgress;
        [SerializeField] private Button m_BtnSignIn;

        [SerializeField] private List<GameObject> m_GoMode1;


        [SerializeField] private List<MainPanel_StageChest> m_LstStageChest;

        [SerializeField] private ChestInfoWindow m_ChestInfoWindow;

        [SerializeField] private TextMeshProUGUI m_TxtStageName;
        [SerializeField] private TextMeshProUGUI m_TxtStageCost;
        [SerializeField] private TextMeshProUGUI m_TxtStageCostRaid;
        [SerializeField] private TextMeshProUGUI m_TxtStageRaid;
        [SerializeField] private TextMeshProUGUI m_TxtStageRaidLock;
        [SerializeField] private TextMeshProUGUI m_TxtLastStageRaid;
        [SerializeField] private Button m_BtnRaid;
        [SerializeField] private Button m_BtnLockedOutpost;
        [SerializeField] private Button m_BtnLockedFever;
        [SerializeField] private List<GameObject> m_LstGoOutpostLocked;

        [SerializeField] private ToggleGroup m_ToggleMode;
        [SerializeField] private Toggle m_ToggleMode1;
        [SerializeField] private Toggle m_ToggleMode2;
        [SerializeField] private GameObject m_GoLockMode;

        [SerializeField] private ChapterRoot m_ChapterRoot;

        public Transform tranSign => m_BtnSignIn.transform;
        public Transform tranBoxReward => m_BtnBoxReward.transform;
        public Transform tranOutpost => m_BtnOutpost.transform;
        public Transform tranPassport => m_BtnPassport.transform;


        private int m_CurLevel = 1;
        private string m_LevelNameKey = "stage";
        public int curLevel
        {
            get { return m_CurLevel; }
            set
            {
                m_CurLevel = value;
                SetArrow(true, true);
                SetArrow(false, true);
                var lstStage = PlayerInfoMgr.data.battleData.GetStageDataList(m_CurMode);
                if (m_CurLevel <= 1)
                {
                    m_CurLevel = 1;
                    SetArrow(true, false);
                }
                else if (m_CurLevel >= lstStage.Count)
                {
                    m_CurLevel = Mathf.Clamp(lstStage.Count, 1, TDStageTable.GetLevelCount(m_CurMode));
                    SetArrow(false, false);
                }
                else
                {
                    SetArrow(true, true);
                    SetArrow(false, true);
                }
                if (1 == lstStage.Count)
                {
                    SetArrow(true, false);
                    SetArrow(false, false);
                }

                PlayerInfoMgr.data.battleData.ChangeSelectStage(m_CurLevel, m_CurMode);
                m_StageData = TDStageTable.GetData(m_CurLevel, m_CurMode);
                //
                m_TxtStageName.text = TDLanguageTable.GetFormat(m_LevelNameKey, m_CurLevel).ToUpper();

                UpdateRaid();
                m_ChapterRoot.UpdateChapter();
            }
        }
        private TDStage m_StageData;
        private bool m_NormalMode = true;
        public bool normalMode
        {
            get
            {
                return m_NormalMode;
            }
            set
            {
                m_NormalMode = value;
                m_LevelNameKey = (m_NormalMode ? "stage" : "mode_2_name");
                m_CurMode = m_NormalMode ? GameMode.Normal : GameMode.Fever;
                PlayerInfoMgr.data.battleData.ChangeGameMode(m_CurMode);
                UpdateMode();
                curLevel = PlayerInfoMgr.data.battleData.UnlockStageId(m_CurMode);
                m_StageData = TDStageTable.GetData(curLevel, m_CurMode);
                GameLogicMgr.S.levelMono?.SetMode(m_CurMode);
                RedPointMgr.S.CheckArrowPoint(m_CurMode);
            }
        }

        private GameMode m_CurMode;

        protected override void OnUIInit()
        {
            base.OnUIInit();
            m_BtnStart.onClick.AddListener(OnClickPlay);
            m_BtnLeft.onClick.AddListener(OnClickLeft);
            m_BtnRight.onClick.AddListener(OnClickRight);
            m_BtnInfo.onClick.AddListener(OnClickInfo);
            m_BtnOutpost.onClick.AddListener(OnClickOutpost);
            m_BtnTarot.onClick.AddListener(OnClickTarot);
            m_BtnRaid.onClick.AddListener(OnClickRaid);
            m_BtnLockedOutpost.onClick.AddListener(OnClickLockedOutpost);
            m_BtnLockedFever.onClick.AddListener(OnClickLockedFever);
            m_BtnBoxReward.onClick.AddListener(OnClickBoxReward);
            m_BtnSetting.onClick.AddListener(() =>
            {
                UIMgr.S.OpenTopPanel(UIID.SettingPanel, null);
            });
            m_BtnAdPack.onClick.AddListener(OnClickPopAd);
            m_BtnNewbiePack.onClick.AddListener(OnClickPopNewbie);
            m_BtnFirstPurchasePack.onClick.AddListener(OnClickPopFirstPurchase);

            m_BtnPassport.onClick.AddListener(() =>
            {
                UIMgr.S.OpenPanel(UIID.PassportPanel);
            });

            m_BtnSignIn.onClick.AddListener(() =>
            {
                UIMgr.S.OpenPanel(UIID.SignInPanel);
            });

            m_ToggleMode1.onValueChanged.AddListener(OnModeChange);
        }

        private void OnModeChange(bool on)
        {
            normalMode = on;
        }

        protected override void OnOpen()
        {
            base.OnOpen();
            DataAnalysisMgr.S.LogPanelOpen(this.uiName, DAPE.ThinkingData, PanelEventLogType.Single);
            RegisterEvent(EventID.OnHideLoading, OnHideLoading);
            //  RegisterEvent(EventID.OnMainPanelChestClick, OnClickChest);
            RegisterEvent(ConsumerEvt.EvtStamina, OnStaminaChange);
            RegisterEvent(EventID.OnRaidCDChanged, OnRaidCdChange);
            RegisterEvent(EventID.OnPlayerLevelChange, OnPlayerLevelChange);
            RegisterEvent(EventID.UnlockFunctionShop, CheckButton);
            RegisterEvent(EngineEventID.OnLanguageTableSwitchFinish, UpdateLanguage);
            OnPlayerLevelChange(0);
            OnStaminaChange(0);
            CheckButton(0);

            PlayerInfoMgr.data.battleData.ChangeGameMode(GameMode.Normal);//返回主界面就重置回普通模式
            bool normal = true;

            if (normal)//nomal主动调用一次 防止isOn没有走ValueChange
            {
                normalMode = normal;
            }
            m_ToggleMode1.isOn = normal;
            m_ToggleMode2.isOn = !normal;

            m_TxtStageCost.text = $"<sprite=0> x{GameDefineHelper.STAGE_STAMINA}";
            m_TxtStageCostRaid.text = m_TxtStageCost.text;

            // m_ChestInfoWindow.Close();
            curLevel = PlayerInfoMgr.data.battleData.UnlockStageId(m_CurMode);


            RedPointMgr.S.CheckOutpost();

            //检查Outpost是否解锁
            m_BtnOutpost.gameObject.SetActive(BotToggleHelper.GetOutpostUnlock());
            m_BtnBoxReward.gameObject.SetActive(BotToggleHelper.GetBoxRewardUnlock());



            ShowOutpostGuide();
            GameLogicMgr.S.StartOutpost();

            // var versionReward = PlayerInfoMgr.data.jewelData.GetChange2NewVersionReward();

            // if (GuideMgr.S.IsGuideFinish(1) && !PlayerInfoMgr.data.jewelData.change2NewVersion && versionReward.Count > 0)
            // {
            //     PopWindowMgr.S.AddPopWindowAction(() =>
            //     {
            //         UIMgr.S.OpenTopPanel(UIID.VersionCompensatePanel, null, versionReward);
            //     });
            // }

            if (PlayerInfoMgr.data.battleData.UnlockStageId(GameMode.Normal) == 5)
            {
                PopWindowMgr.S.AddPopWindowAction(() =>
                {
                    UIMgr.S.OpenTopPanel(EngineUI.RatePanel, null);
                });
            }

            if (GuideMgr.S.IsGuideFinish(2) && !PlayerPrefs.HasKey("Autoshowiapnoad") && UnityIapMgr.S.IsIapInit && BotToggleHelper.GetUnlock(BottomMenuType.Shop))
            {
                if (!IapRewardMgr.S.NeedClose(GameDefineHelper.IAP_PACK_NOAD))
                {
                    PopWindowMgr.S.AddPopWindowAction(() =>
                    {
                        var data = IapRewardMgr.S.dicIapReward[GameDefineHelper.IAP_PACK_NOAD];
                        UIMgr.S.OpenPanel(UIID.GiftpackInfoPanel, data.productId, data);
                        PlayerPrefs.SetInt("Autoshowiapnoad", 1);
                    });
                }
            }

            if (BotToggleHelper.GetUnlock(BottomMenuType.Shop) && PlayerInfoMgr.data.passDay > 0 && !PlayerInfoMgr.data.shopData.HasSuperCard()
            && !PlayerInfoMgr.data.shopData.dailyShowSuperGiftpack)
            {
                PopWindowMgr.S.AddPopWindowAction(() =>
               {
                   var data = IapRewardMgr.S.dicIapReward[GameDefineHelper.IAP_PACK_SUPER];
                   UIMgr.S.OpenPanel(UIID.GiftpackInfoPanel, data.productId, data);
               });
                PlayerInfoMgr.data.shopData.SetDailyShowSuperGiftpack();
            }

            if (PlayerInfoMgr.data.battleData.UnlockStageId() > GameDefineHelper.POP_IAP_LEVEL && !PlayerInfoMgr.data.shopData.HasBuyNewBie() && !PlayerInfoMgr.data.shopData.dailyShowNewbieGiftpack && PlayerInfoMgr.data.battleData.UnlockStageId() >= 4)
            {
                var record = PlayerInfoMgr.data.shopData.Get3DayData(GameDefineHelper.IAP_PACK_3DAY_2);
                if (!record.dailyGet && !record.closed)
                {
                    PopWindowMgr.S.AddPopWindowAction(() =>
                    {
                        UIMgr.S.OpenPanel(UIID.Giftpack3DayInfoPanel, GameDefineHelper.IAP_PACK_3DAY_2);

                    });
                    PlayerInfoMgr.data.shopData.SetDailyShowNewbieGiftpack();
                }
            }

            if (PlayerInfoMgr.data.battleData.UnlockStageId() > GameDefineHelper.POP_IAP_LEVEL && !PlayerInfoMgr.data.shopData.HasBuyFirstPurchase()
            && !PlayerInfoMgr.data.shopData.dailyShowFirstPurchaseGiftpack)
            {
                var record = PlayerInfoMgr.data.shopData.Get3DayData(GameDefineHelper.IAP_PACK_3DAY_1);
                if (!record.dailyGet && !record.closed)
                {
                    PopWindowMgr.S.AddPopWindowAction(() =>
                    {
                        UIMgr.S.OpenPanel(UIID.Giftpack3DayInfoPanel, GameDefineHelper.IAP_PACK_3DAY_1);
                    });
                    PlayerInfoMgr.data.shopData.SetDailyShowFirstPurchaseGiftpack();
                }

            }


            RedPointMgr.S.CheckRune();
            RedPointMgr.S.CheckShop();


        }

        private void CheckUnlockFunction()
        {
            if (BotToggleHelper.GetBoxRewardUnlock() && !PlayerPrefs.HasKey("unlockfuctionboxreward"))
            {
                PopWindowMgr.S.AddPopWindowAction(() =>
                {
                    UIMgr.S.OpenTopPanel(UIID.UnlockFunctionPanel, null, GameFunctionType.BoxReward);
                    PlayerPrefs.SetInt("unlockfuctionboxreward", 0);
                });
            }
            if (BotToggleHelper.GetSignInUnlock() && !PlayerPrefs.HasKey("unlockfuctionsignin"))
            {
                PopWindowMgr.S.AddPopWindowAction(() =>
                {
                    UIMgr.S.OpenTopPanel(UIID.UnlockFunctionPanel, null, GameFunctionType.Sign);
                    PlayerPrefs.SetInt("unlockfuctionsignin", 0);
                });
            }
            if (BotToggleHelper.GetOutpostUnlock() && !PlayerPrefs.HasKey("unlockfuctionoutpost"))
            {
                PopWindowMgr.S.AddPopWindowAction(() =>
                {
                    UIMgr.S.OpenTopPanel(UIID.UnlockFunctionPanel, null, GameFunctionType.Outpost);
                    PlayerPrefs.SetInt("unlockfuctionoutpost", 0);
                });
            }
            if (BotToggleHelper.GetPassportUnlock() && !PlayerPrefs.HasKey("unlockfuctionpassport"))
            {
                PopWindowMgr.S.AddPopWindowAction(() =>
                {
                    UIMgr.S.OpenTopPanel(UIID.UnlockFunctionPanel, null, GameFunctionType.Passport);
                    PlayerPrefs.SetInt("unlockfuctionpassport", 0);
                });
            }
            if (BotToggleHelper.GetDungeonCoinUnlock() && !PlayerPrefs.HasKey("unlockfuctioncoindungeon"))
            {
                PopWindowMgr.S.AddPopWindowAction(() =>
                {
                    UIMgr.S.OpenTopPanel(UIID.UnlockFunctionPanel, null, GameFunctionType.CoinDungeon);
                    PlayerPrefs.SetInt("unlockfuctioncoindungeon", 0);
                });
            }
            if (BotToggleHelper.GetDungeonSpellUnlock() && !PlayerPrefs.HasKey("unlockfuctionspelldungeon"))
            {
                PopWindowMgr.S.AddPopWindowAction(() =>
                {
                    UIMgr.S.OpenTopPanel(UIID.UnlockFunctionPanel, null, GameFunctionType.SpellDungeon);
                    PlayerPrefs.SetInt("unlockfuctionspelldungeon", 0);
                });
            }
            if (BotToggleHelper.GetDungeonHeroUnlock() && !PlayerPrefs.HasKey("unlockfuctionherodungeon"))
            {
                PopWindowMgr.S.AddPopWindowAction(() =>
                {
                    UIMgr.S.OpenTopPanel(UIID.UnlockFunctionPanel, null, GameFunctionType.HeroDungeon);
                    PlayerPrefs.SetInt("unlockfuctionherodungeon", 0);
                });
            }
            if (BotToggleHelper.GetDungeonDragonUnlock() && !PlayerPrefs.HasKey("unlockfuctiondragondungeon"))
            {
                PopWindowMgr.S.AddPopWindowAction(() =>
                {
                    UIMgr.S.OpenTopPanel(UIID.UnlockFunctionPanel, null, GameFunctionType.DragonDungeon);
                    PlayerPrefs.SetInt("unlockfuctiondragondungeon", 0);
                });
            }


            if (BotToggleHelper.GetUnlock(BottomMenuType.Shop) && !GuideMgr.S.IsGuideFinish(7))
            {
                if (PlayerPrefs.HasKey("unlockfuctionshop"))
                {
                    //EventSystem.S.Send(EventID.UnlockFunctionShop);
                }
                else
                {
                    PopWindowMgr.S.AddPopWindowAction(() =>
                    {
                        UIMgr.S.OpenTopPanel(UIID.UnlockFunctionPanel, null, GameFunctionType.Shop);
                        PlayerPrefs.SetInt("unlockfuctionshop", 0);
                    });
                }
            }
            else if (BotToggleHelper.GetUnlock(BottomMenuType.Card) && !GuideMgr.S.IsGuideFinish(2))
            {
                if (PlayerPrefs.HasKey("unlockfuctioncard"))
                {
                    if (UIMgr.S.FindPanel(UIID.BottomBarPanel) != null)
                    {
                        EventSystem.S.Send(EventID.UnlockFunctionCard);
                    }
                }
                else
                {
                    PopWindowMgr.S.AddPopWindowAction(() =>
                    {
                        UIMgr.S.OpenTopPanel(UIID.UnlockFunctionPanel, null, GameFunctionType.Card);
                        PlayerPrefs.SetInt("unlockfuctioncard", 0);
                    });
                }

            }
        }

        private void UpdateLanguage(int key, params object[] param)
        {
            try
            {
                m_TxtStageRaid.text = $"{(m_HasRaidFree ? "" : "<sprite=0> ")}{TDLanguageTable.Get("btn_raid").ToUpper()}";
                m_TxtStageRaidLock.text = $"{(m_HasRaidFree ? "" : "<sprite=0> ")}{TDLanguageTable.Get("btn_raid").ToUpper()}";
                m_TxtStageName.text = TDLanguageTable.GetFormat(m_LevelNameKey, m_CurLevel).ToUpper();
            }
            catch (Exception e)
            {
                Log.e(e.ToString());
            }
        }

        private void ShowOutpostGuide()
        {
            // if (!PlayerPrefs.HasKey("showoutpostguide") && BotToggleHelper.GetOutpostUnlock())
            // {
            //     PopWindowMgr.S.AddPopWindowAction(() =>
            //     {
            //         UIMgr.S.OpenTopPanel(UIID.OutpostGuidePanel, null);
            //     });
            //     RedPointMgr.S.CheckOutpostGuide();
            //     PlayerPrefs.SetInt("showoutpostguide", 1);
            // }
            // if (!GuideMgr.S.IsGuideFinish(6) && BotToggleHelper.GetFeverUnlock())
            // {
            //     PopWindowMgr.S.AddPopWindowAction(() =>
            //     {
            //         EventSystem.S.Send(EventID.OnGuideFeverMode);
            //     });
            // }
        }

        private void OnPlayerLevelChange(int key, params object[] param)
        {
            if (param.Length > 0)
            {
                int level = (int)PlayerInfoMgr.data.playerLv;
                PopWindowMgr.S.AddPopWindowAction(() =>
               {
                   UIMgr.S.OpenTopPanel(UIID.LevelUpPanel, null, level);
               });
            }
        }


        private void OnStaminaChange(int key, params object[] param)
        {

            bool enough = PlayerInfoMgr.data.stamina >= GameDefineHelper.STAGE_STAMINA;
            if (SOMgr.S != null)
            {
                m_TxtStageCost.color = enough ? Color.white : SOMgr.S.colorUnable;
                m_TxtStageCostRaid.color = enough ? Color.white : SOMgr.S.colorUnable;
            }
            m_BtnRaid.interactable = enough && (PlayerInfoMgr.data.battleData.CanAdRaid() || PlayerInfoMgr.data.battleData.HasDailyFreeRaid() || PlayerInfoMgr.data.shopData.canSweepFree);
            m_TxtPassportLevel.text = PlayerInfoMgr.data.passportData.level.ToString();
            m_TxtPassportProgress.text = $"{PlayerInfoMgr.data.passportData.exp}/{PlayerInfoMgr.data.passportData.GetExpToNextLevel()}";
            if (PlayerInfoMgr.data.passportData.GetExpToNextLevel() == 0)
            {
                m_TxtPassportProgress.text = "";
                m_SldPassportProgress.value = 1;
            }
            else
            {
                float value = (float)PlayerInfoMgr.data.passportData.exp / PlayerInfoMgr.data.passportData.GetExpToNextLevel();
                m_SldPassportProgress.value = value;
            }
        }

        private void OnClickLockedOutpost()
        {
            FloatMessage.S.ShowMsg(TDLanguageTable.GetFormat("unlock_function_tip", GameDefineHelper.OUTPOST_LEVEL));
        }
        private void OnClickLockedFever()
        {
            FloatMessage.S.ShowMsg(TDLanguageTable.GetFormat("unlock_function_tip", GameDefineHelper.FEVER_LEVEL));
        }

        private bool m_HasTrigger = false;
        private void OnHideLoading(int key, object[] param)
        {
            //触发引导
            CustomExtensions.CallWithDelay(this, () =>
            {
                EventSystem.S.Send(EventID.OnGuidePlayGame);
                if (BotToggleHelper.GetUnlock(BottomMenuType.Shop) && !GuideMgr.S.IsGuideFinish(7))
                {
                    if (PlayerPrefs.HasKey("unlockfuctionshop"))
                    {
                        EventSystem.S.Send(EventID.UnlockFunctionShop);
                    }
                }
                EventSystem.S.Send(EventID.OnGuidePlayAgain);
                if (m_BtnBoxReward.gameObject.activeInHierarchy)
                {
                    EventSystem.S.Send(EventID.OnGuideClickBoxReward);
                }
                if (UIMgr.S.FindPanel(UIID.BottomBarPanel) != null)
                {
                    EventSystem.S.Send(EventID.UnlockFunctionCard);
                }
                EventSystem.S.Send(EventID.OnGuideCheckSelection);
                EventSystem.S.Send(EventID.OnGuideRaiseStarAgain);

                PopWindowMgr.S.AddPopWindowAction(() =>
                {
                    if (!GuideMgr.S.IsGuideFinish(18))
                    {
                        EventSystem.S.Send(EventID.UnlockFunctionCoinDungeon);
                    }
                });

                m_HasTrigger = true;
            }, 0.1f);
        }

        protected override void OnPanelOpen(params object[] args)
        {
            base.OnPanelOpen(args);
            InitRaid();
            //检测是否弹解锁弹框
            CheckUnlockFunction();
        }

        private int m_GuideTimer;
        protected override void OnPanelShowComplete()
        {
            base.OnPanelShowComplete();
            if (m_HasTrigger) return;

            m_GuideTimer = Timer.S.Post2Scale((count) =>
             {
                 //Log.e(count);
                 if (UIMgr.S.FindPanel(UIID.BottomBarPanel) != null)
                 {
                     if (UIMgr.S.FindPanel(UIID.BottomBarPanel) != null)
                     {
                         EventSystem.S.Send(EventID.UnlockFunctionCard);
                     }
                     // Log.e("BottomBar不为空 {0}", count);
                     if (BotToggleHelper.GetUnlock(BottomMenuType.Shop) && !GuideMgr.S.IsGuideFinish(7))
                     {
                         if (PlayerPrefs.HasKey("unlockfuctionshop"))
                         {
                             EventSystem.S.Send(EventID.UnlockFunctionShop);
                         }
                     }

                     EventSystem.S.Send(EventID.OnGuidePlayAgain);
                     EventSystem.S.Send(EventID.OnGuideCheckSelection);
                     EventSystem.S.Send(EventID.OnGuideRaiseStarAgain);
                     Timer.S.Cancel(m_GuideTimer);
                 }
             }, 0.1f, -1);

            PopWindowMgr.S.AddPopWindowAction(() =>
            {
                if (m_BtnBoxReward != null && m_BtnBoxReward.gameObject.activeInHierarchy)
                {
                    EventSystem.S.Send(EventID.OnGuideClickBoxReward);
                }
            });
        }

        protected override void OnClose()
        {
            base.OnClose();
            GameLogicMgr.S.CloseOutpost();
            if (m_RaidTimer > 0)
            {
                Timer.S.Cancel(m_RaidTimer);
                m_RaidTimer = -1;
            }
            if (m_GuideTimer > 0)
            {
                Timer.S.Cancel(m_GuideTimer);
                m_GuideTimer = -1;
            }
        }

        protected override void OnPanelHideComplete()
        {
            base.OnPanelHideComplete();
        }


        private void OnClickPlay()
        {
            UIMgr.S.OpenPanel(UIID.StageInfoPanel, m_CurLevel);
        }

        private void OnClickPopAd()
        {
            if (!PlayerInfoMgr.data.shopData.HasNoAdCard())
            {
                var data = IapRewardMgr.S.dicIapReward[GameDefineHelper.IAP_PACK_NOAD];
                UIMgr.S.OpenPanel(UIID.GiftpackInfoPanel, data.productId, data);
                CheckButton(0);
            }
            else
            {
                //跳转商店
                EventSystem.S.Send(EventID.OnOpenShopPage, 4);
            }

        }
        private void OnClickPopNewbie()
        {
            UIMgr.S.OpenPanel(UIID.Giftpack3DayInfoPanel, GameDefineHelper.IAP_PACK_3DAY_2);
            CheckButton(0);
        }
        private void OnClickPopFirstPurchase()
        {
            UIMgr.S.OpenPanel(UIID.Giftpack3DayInfoPanel, GameDefineHelper.IAP_PACK_3DAY_1);
            CheckButton(0);
        }

        private void CheckButton(int key, params object[] param)
        {
            bool shopUnlock = BotToggleHelper.GetUnlock(BottomMenuType.Shop);

            bool showSignIn = BotToggleHelper.GetSignInUnlock();
            //去广告按钮
            var data = PlayerInfoMgr.data.shopData.GetMonthlyCardData(GameDefineHelper.IAP_PACK_NOAD);
            bool showAdPackBtn = (!(data.dailyGet || data.closed)) && shopUnlock && PlayerInfoMgr.data.battleData.UnlockStageId() >= 4;
            m_BtnAdPack.gameObject.SetActive(showAdPackBtn);

            //新手礼包
            bool showNewbiePackBtn = false;
            if (UnityIapMgr.S.IsIapInit)
            {
                showNewbiePackBtn = PlayerInfoMgr.data.battleData.UnlockStageId() > GameDefineHelper.POP_IAP_LEVEL;
            }
            var recordNewbie = PlayerInfoMgr.data.shopData.Get3DayData(GameDefineHelper.IAP_PACK_3DAY_2);
            var recordFP = PlayerInfoMgr.data.shopData.Get3DayData(GameDefineHelper.IAP_PACK_3DAY_1);
            m_BtnNewbiePack.gameObject.SetActive(showNewbiePackBtn && PlayerInfoMgr.data.battleData.UnlockStageId() > GameDefineHelper.POP_IAP_LEVEL && !recordNewbie.closed);
            m_BtnFirstPurchasePack.gameObject.SetActive(showNewbiePackBtn && PlayerInfoMgr.data.battleData.UnlockStageId() > GameDefineHelper.POP_IAP_LEVEL && !recordFP.closed);
            //通行证按钮
            m_BtnPassport.gameObject.SetActive(BotToggleHelper.GetPassportUnlock());
            //卡牌系统
            m_BtnTarot.gameObject.SetActive(false);
            //签到
            m_BtnSignIn.gameObject.SetActive(showSignIn);
        }


        private bool m_HasRaidFree;
        private int m_RaidTimer;
        private void InitRaid()
        {
            m_HasRaidFree = PlayerInfoMgr.data.shopData.canSweepFree || PlayerInfoMgr.data.battleData.HasDailyFreeRaid();
            m_TxtStageRaid.text = $"{(m_HasRaidFree ? "" : "<sprite=0> ")}{TDLanguageTable.Get("btn_raid").ToUpper()}";
            m_TxtStageRaidLock.text = $"{(m_HasRaidFree ? "" : "<sprite=0> ")}{TDLanguageTable.Get("btn_raid").ToUpper()}";
            // x{GameDefineHelper.STAGE_STAMINA}
            if (!m_HasRaidFree)
            {
                OnRaidCdChange(0);
            }
        }

        private Image m_ImgRaid;
        private bool m_RaidLastStage = false;
        private void UpdateRaid()
        {
            bool perfectFinish = curLevel < PlayerInfoMgr.data.battleData.UnlockStageId(m_CurMode) && curLevel != 1;

            m_TxtLastStageRaid.text = "";
            m_RaidLastStage = false;
            if (curLevel == PlayerInfoMgr.data.battleData.UnlockStageId(m_CurMode) && curLevel > 2)
            {
                m_TxtLastStageRaid.text = TDLanguageTable.GetFormat("stage", curLevel - 1);
                m_RaidLastStage = true;
            }

            m_TxtStageRaid.gameObject.SetActive(perfectFinish || m_RaidLastStage);
            m_TxtStageRaidLock.gameObject.SetActive(!perfectFinish && !m_RaidLastStage);

            if (m_ImgRaid == null)
            {
                m_ImgRaid = m_BtnRaid.GetComponent<Image>();
            }
            if (m_ImgRaid != null)
            {
                m_ImgRaid.material = (perfectFinish || m_RaidLastStage) ? null : SOMgr.S.matDark;
            }
        }

        private void UpdateMode()
        {
            m_GoMode1.ForEach(go =>
            {
                go.SetActive(m_NormalMode);
            });
            if (!m_NormalMode)
            {
                EventSystem.S.Send(EventID.OnGuidePlayFever);
            }
        }

        private void OnRaidCdChange(int key, params object[] param)
        {
            bool canRaid = PlayerInfoMgr.data.battleData.CanAdRaid();
            m_HasRaidFree = PlayerInfoMgr.data.shopData.canSweepFree || PlayerInfoMgr.data.battleData.HasDailyFreeRaid();
            if (!canRaid)
            {
                int sec = PlayerInfoMgr.data.battleData.GetAdRaidCDRemain();
                m_TxtStageRaid.text = DateFormatHelper.FormatTime(sec);
                if (m_RaidTimer <= 0)
                {
                    m_RaidTimer = Timer.S.Post2Scale(count =>
                    {
                        OnRaidCdChange(0);
                    }, 1, -1);
                }
            }
            else
            {
                m_TxtStageRaid.text = $"{(m_HasRaidFree ? "" : "<sprite=0> ")}{TDLanguageTable.Get("btn_raid").ToUpper()}";
                m_TxtStageRaidLock.text = $"{(m_HasRaidFree ? "" : "<sprite=0> ")}{TDLanguageTable.Get("btn_raid").ToUpper()}";
                if (m_RaidTimer > 0)
                {
                    Timer.S.Cancel(m_RaidTimer);
                    m_RaidTimer = -1;
                }
            }
            OnStaminaChange(0);
        }


        private void OnClickRaid()
        {
#if UNITY_EDITOR
            GameMode mode = GameMode.Test;
            GameLogicMgr.S.StartGame(1, mode);
            UIMgr.S.ClosePanelAsUIID(UIID.MainPanel);
            UIMgr.S.ClosePanelAsUIID(UIID.BottomBarPanel);
            UITopPanel.S.ShowAll(false);
            TipsPanel.S.HideAllTips();
            return;
#endif

            bool perfectFinish = curLevel < PlayerInfoMgr.data.battleData.UnlockStageId(m_CurMode) && curLevel != 1;
            m_RaidLastStage = false;
            if (curLevel == PlayerInfoMgr.data.battleData.UnlockStageId(m_CurMode) && curLevel > 2)
            {
                m_RaidLastStage = true;
            }
            if (!perfectFinish && !m_RaidLastStage)
            {
                FloatMessage.S.ShowMsg(TDLanguageTable.Get("raid_desc"));
                return;
            }
            //判断看广告还是直接拿
            //判断体力是否充足
            //通用获得奖励逻辑
            int level = m_RaidLastStage ? curLevel - 1 : curLevel;
            m_HasRaidFree = PlayerInfoMgr.data.shopData.canSweepFree || PlayerInfoMgr.data.battleData.HasDailyFreeRaid();
            if (m_HasRaidFree)
            {
                if (PlayerInfoMgr.data.UseStamina(GameDefineHelper.STAGE_STAMINA))
                {
                    int rewardId = TDStageTable.GetData(level, m_CurMode).highRewardId;
                    TDStageRewards rewardData = TDStageRewardsTable.GetData(rewardId);
                    var lst = GameRewardHelper.ChangeRewardToSure(rewardData.normalPassReward);
                    lst.Add(new RewardInfo(TDStageTable.GetData(level, m_CurMode).waveDropAllCoin));
                    UIMgr.S.OpenPanel(UIID.GetRewardPanel, lst);
                    //更新ui
                    if (PlayerInfoMgr.data.battleData.HasDailyFreeRaid())
                    {
                        FloatMessage.S.ShowMsg(TDLanguageTable.GetFormat("sweep_remain", PlayerInfoMgr.data.battleData.dailyFreeRaid - 1));
                    }
                    PlayerInfoMgr.data.battleData.UseFreeRaid();
                    InitRaid();//这里再重新赋值一下UI

                    DataAnalysisMgr.S.ResetEventMap()
                    .AddEventParam("free", true)
                    .AddEventParam("stage", level)
                    .AddEventParam("mode", (int)m_CurMode)
                    .CustomEventDic("p_raid", DAPE.ThinkingData);
                }
                else
                {
                    UIMgr.S.OpenPanel(UIID.StaminaSupplyPanel);
                }
            }
            else
            {
                if (!PlayerInfoMgr.data.battleData.CanAdRaid())
                {
                    return;
                }

                if (PlayerInfoMgr.data.stamina >= GameDefineHelper.STAGE_STAMINA)
                {
                    AdsPlayMgr.S.PlayRewardAd("raid", click =>
                    {
                        if (PlayerInfoMgr.data.UseStamina(GameDefineHelper.STAGE_STAMINA))
                        {
                            PlayerInfoMgr.data.battleData.SetAdRaidCDTime();
                            int rewardId = TDStageTable.GetData(level, m_CurMode).highRewardId;
                            TDStageRewards rewardData = TDStageRewardsTable.GetData(rewardId);
                            var lst = GameRewardHelper.ChangeRewardToSure(rewardData.normalPassReward);
                            lst.Add(new RewardInfo(TDStageTable.GetData(level, m_CurMode).waveDropAllCoin));
                            UIMgr.S.OpenPanel(UIID.GetRewardPanel, lst);
                            AdsPlayMgr.S.PushGetResourceByAd("raid", lst);

                            DataAnalysisMgr.S.ResetEventMap()
                            .AddEventParam("free", false)
                            .AddEventParam("stage", level)
                            .AddEventParam("mode", (int)m_CurMode)
                            .CustomEventDic("p_raid", DAPE.ThinkingData);

                            if (!PlayerInfoMgr.data.shopData.dailyShowPremiumGiftpack)
                            {
                                PopWindowMgr.S.AddPopWindowAction(() =>
                                {
                                    try
                                    {
                                        var data = IapRewardMgr.S.dicIapReward[GameDefineHelper.IAP_PACK_PREMIUM];
                                        PlayerInfoMgr.data.shopData.SetDailyShowPremiumGiftpack();
                                        UIMgr.S.OpenPanel(UIID.GiftpackInfoPanel, data.productId, data);
                                    }
                                    catch (Exception e)
                                    {
                                        Log.e(e.ToString());
                                    }
                                });
                            }
                        }
                    });
                }

            }
        }

        void OnClickBoxReward()
        {
            UIMgr.S.OpenPanel(UIID.BoxRewardPanel);
        }

        private void OnClickOutpost()
        {
            UIMgr.S.OpenPanel(UIID.OutpostPanel);
        }

        private void OnClickTarot()
        {
            // UIMgr.S.OpenPanel(UIID.TarotPanel);
        }

        private void OnClickLeft()
        {
            curLevel--;
        }

        private void OnClickRight()
        {
            curLevel++;
        }

        private void OnClickInfo()
        {
            UIMgr.S.OpenPanel(UIID.StageInfoPanel, m_CurLevel);
        }

        private void SetArrow(bool left, bool show)
        {
            if (left)
            {
                m_BtnLeft.gameObject.SetActive(show);
            }
            else
            {
                m_BtnRight.gameObject.SetActive(show);
            }
        }

    }
}

