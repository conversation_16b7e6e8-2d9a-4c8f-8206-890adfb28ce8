//Panel模版
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Qarth;
using DG.Tweening;
using TMPro;
using System.Collections;
using System;

namespace GameWish.Game
{

    public class DragonDungeonStagePanel : AbstractPanel
    {

        [SerializeField] private TextMeshP<PERSON>UGUI m_TxtCountDown;
        [SerializeField] private TextMeshP<PERSON><PERSON>GUI m_TxtBestRecord;
        [SerializeField] private TextMeshP<PERSON>UGUI m_TxtRemainTimes;

        [SerializeField] private TextMeshProUGUI m_TxtEnterAd;
        [SerializeField] private Button m_BtnClose;
        [SerializeField] private Button m_BtnEnter;
        [SerializeField] private Button m_BtnEnterAd;



        private GameMode m_GameMode;
        private DungeonData m_DungeonData;
        private int m_TimerCountDown = 0;

        //<color=#2ABC1B>
        protected override void OnUIInit()
        {
            base.OnUIInit();
            m_BtnClose.onClick.AddListener(CloseSelfPanel);
            m_BtnEnter.onClick.AddListener(OnClickEnter);
            m_BtnEnterAd.onClick.AddListener(OnClickEnterAd);
        }


        protected override void OnOpen()
        {
            base.OnOpen();
            DataAnalysisMgr.S.LogPanelOpen(this.uiName, DAPE.ThinkingData, PanelEventLogType.Single);
        }

        protected override void OnPanelOpen(params object[] args)
        {
            base.OnPanelOpen(args);
            OpenDependPanel(UIID.MainBgPanel, -1, null);
            m_GameMode = (GameMode)args[0];
            m_DungeonData = PlayerInfoMgr.data.battleData.GetDungeonData(m_GameMode);
            Refresh();


        }

        protected override void OnClose()
        {
            base.OnClose();
            if (m_TimerCountDown > 0)
            {
                Timer.S.Cancel(m_TimerCountDown);
            }
        }

        private bool m_FreeChallenge = true;
        private void Refresh()
        {
            m_TxtEnterAd.text = $"<sprite name=\"ad\">{TDLanguageTable.Get("btn_challenge")}";
            //判断是广告挑战还是普通挑战按钮还是不能挑战
            m_FreeChallenge = m_DungeonData.dailyFreeRaidTimes > 0;
            m_BtnEnter.gameObject.SetActive(m_FreeChallenge);
            m_BtnEnterAd.gameObject.SetActive(!m_FreeChallenge);
            bool canChallenge = m_DungeonData.dailyRaidTimes > 0;
            // #if UNITY_EDITOR
            //             Log.e("编辑器不限制次数");
            //             m_BtnEnter.interactable = true;
            //             m_BtnEnterAd.interactable = true;
            // #else
            m_BtnEnter.interactable = canChallenge;
            m_BtnEnterAd.interactable = canChallenge;
            // #endif
            int remain = m_DungeonData.dailyRaidTimes;
            m_TxtRemainTimes.text = TDLanguageTable.GetFormat("hero_dungeon_remain", $"<color=#4AEF61>{remain}/5</color>");
            m_TxtBestRecord.text = $"{m_DungeonData.bestRecord}M";
            UpdateRemainTime(1);
            if (m_TimerCountDown > 0)
            {
                Timer.S.Cancel(m_TimerCountDown);
            }
            m_TimerCountDown = Timer.S.Post2Scale(UpdateRemainTime, 1, -1);


        }

        private void OnClickEnter()
        {
            //打开页面
            PlayerInfoMgr.data.battleData.ChangeGameMode(m_GameMode);
            GameLogicMgr.S.StartGame(PlayerInfoMgr.data.battleData.GetModeData(m_GameMode).curStageId, m_GameMode);
            m_DungeonData.UseFreeRaid();
            UIMgr.S.ClosePanelAsUIID(UIID.DungeonPanel);
            UIMgr.S.ClosePanelAsUIID(UIID.HeroDungeonStagePanel);
            UIMgr.S.ClosePanelAsUIID(UIID.DragonDungeonStagePanel);
            UIMgr.S.ClosePanelAsUIID(UIID.BottomBarPanel);
            UITopPanel.S.ShowAll(false);
            TipsPanel.S.HideAllTips();
        }
        private void OnClickEnterAd()
        {
            AdsPlayMgr.S.PlayRewardAd("dungeon_dragon", click =>
            {
                //打开页面
                PlayerInfoMgr.data.battleData.ChangeGameMode(m_GameMode);
                GameLogicMgr.S.StartGame(PlayerInfoMgr.data.battleData.GetModeData(m_GameMode).curStageId, m_GameMode);
                m_DungeonData.UseAdRaid();
                UIMgr.S.ClosePanelAsUIID(UIID.DungeonPanel);
                UIMgr.S.ClosePanelAsUIID(UIID.HeroDungeonStagePanel);
                UIMgr.S.ClosePanelAsUIID(UIID.DragonDungeonStagePanel);
                UIMgr.S.ClosePanelAsUIID(UIID.BottomBarPanel);
                UITopPanel.S.ShowAll(false);
                TipsPanel.S.HideAllTips();
            });

        }

        private void UpdateRemainTime(int count)
        {
            string countdown = DateFormatHelper.FormatTime((int)(DateTime.Now.Date.AddDays(1) - DateTime.Now).TotalSeconds, true);
            m_TxtCountDown.text = TDLanguageTable.GetFormat("dungeon_reset", $":<color=#FFED53>{countdown}</color>");
        }

    }
}

