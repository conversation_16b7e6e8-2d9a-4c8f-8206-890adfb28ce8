using System.Collections;
using System.Collections.Generic;
using Qarth;
using UnityEngine;


namespace GameWish.Game
{
	public class BotToggleHelper
	{
		private static string m_CardUnlockKey = "m_CardUnlockKey";

		private static string m_ShopUnlockKey = "m_ShopUnlockKey";
		private static string m_DungeonUnlockKey = "m_DungeonUnlockKey";


		public static bool GetSignInUnlock()
		{
			return PlayerInfoMgr.data.battleData.UnlockStageId() > GameDefineHelper.SIGNIN_LEVEL;
		}
		public static bool GetPassportUnlock()
		{
			return PlayerInfoMgr.data.battleData.UnlockStageId() > GameDefineHelper.PASSPORT_UNLOCK;
		}
		public static bool GetDungeonCoinUnlock()
		{
			return PlayerInfoMgr.data.battleData.UnlockStageId() > GameDefineHelper.DUNGEON_COIN_LEVEL;
		}
		public static bool GetDungeonSpellUnlock()
		{
			return PlayerInfoMgr.data.battleData.UnlockStageId() > GameDefineHelper.DUNGEON_SPELL_LEVEL;
		}
		public static bool GetDungeonHeroUnlock()
		{
			return PlayerInfoMgr.data.battleData.UnlockStageId() > GameDefineHelper.DUNGEON_HERO_LEVEL;
		}
		public static bool GetDungeonDragonUnlock()
		{
			return PlayerInfoMgr.data.battleData.UnlockStageId() > GameDefineHelper.DUNGEON_DRAGON_LEVEL;
		}
		public static bool GetFeverUnlock()
		{
			return false;
			//return PlayerInfoMgr.data.battleData.UnlockStageId() > GameDefineHelper.FEVER_LEVEL;
		}
		public static bool GetOutpostUnlock()
		{
			return PlayerInfoMgr.data.battleData.UnlockStageId() > GameDefineHelper.OUTPOST_LEVEL;
		}
		public static bool GetBoxRewardUnlock()
		{
			return PlayerInfoMgr.data.battleData.UnlockStageId() > GameDefineHelper.BOXREWARD_LEVEL;
		}

		public static bool GetUnlock(BottomMenuType type)
		{
			bool unlock = true;
			switch (type)
			{
				//case BottomMenuType.Upgrade:
				case BottomMenuType.Stage:
					break;
				case BottomMenuType.Artifact:
					unlock = false;
					break;
				case BottomMenuType.Card:
					if (PlayerPrefs.GetInt(m_CardUnlockKey, 0) == 0)
					{
						unlock = PlayerInfoMgr.data.battleData.UnlockStageId() > GameDefineHelper.CARD_LEVEL;
						if (unlock)
						{
							PlayerPrefs.SetInt(m_CardUnlockKey, 1);
							RedPointMgr.S.CheckHero();
						}
					}
					unlock = PlayerPrefs.GetInt(m_CardUnlockKey, 0) == 1;
					break;
				case BottomMenuType.Shop:
					if (PlayerPrefs.GetInt(m_ShopUnlockKey, 0) == 0)
					{
						unlock = PlayerInfoMgr.data.battleData.UnlockStageId() > GameDefineHelper.SHOP_LEVEL;
						if (unlock)
						{
							PlayerPrefs.SetInt(m_ShopUnlockKey, 1);
							RedPointMgr.S.CheckShop();
							RedPointMgr.S.CheckPassport();
						}
					}
					unlock = PlayerPrefs.GetInt(m_ShopUnlockKey, 0) == 1;
					break;
				case BottomMenuType.Dungeon:
					if (PlayerPrefs.GetInt(m_DungeonUnlockKey, 0) == 0)
					{
						unlock = PlayerInfoMgr.data.battleData.UnlockStageId() > GameDefineHelper.DUNGEON_COIN_LEVEL;
						if (unlock)
						{
							PlayerPrefs.SetInt(m_DungeonUnlockKey, 1);
							// RedPointMgr.S.CheckShop();
							// RedPointMgr.S.CheckPassport();
						}
					}
					unlock = PlayerPrefs.GetInt(m_DungeonUnlockKey, 0) == 1;
					break;
				default:
					break;
			}
			return unlock;
		}

		public static bool CanShowAd()
		{
			return true;// PlayerInfoMgr.data.battleData.upgradeTimes >= m_AdShowUpgradeTimes;
		}

		public static bool CanShowSpeedUpBtn()
		{
			return PlayerInfoMgr.data.battleData.UnlockStageId() >= 6;
		}
	}

}