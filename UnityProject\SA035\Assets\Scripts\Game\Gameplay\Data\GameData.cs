﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Qarth;

namespace GameWish.Game
{
    public class GameData : IDataClass
    {
        public string strFirstStartTime;
        public string strFirstStartDayTime;
        public int day;
        public string lastPlayTimeString = "0";
        public int passDay = -1;

        public EInt dailyAdCount = 0;
        public EInt playAdTotalCount;
        public EInt playerLv = 1;
        public EInt playerExp;
        public bool levelUpShowPanel = false;
        public EInt levelPlayTimes;
        public int newVersion = 0;
        public int gameplayMinute = 0;

        public ConsumerData consumerData;
        public BattleData battleData;
        public GameTimestampData timestampData;
        public SignInData signInData;
        public CardData cardData;
        public ShopData shopData;
        public GemSourceData gemSourceData;
        public OutpostData outpostData;
        public PassportData passportData;
        public TaskData taskData;
        public DiceSelectionData diceSelectionData;


        public GameData()
        {
            SetDirtyRecorder(PlayerInfoMgr.dataDirtyRecorder);
        }

        public override void InitWithEmptyData()
        {
            Log.w("InitWithEmptyData");
            newVersion = 82;
            playerLv = 1;
            strFirstStartTime = DateTime.Now.ToStringEx();
            strFirstStartDayTime = DateTime.Now.Date.ToStringEx();
            SetDataDirty();
        }

        public override void OnDataLoadFinish()
        {
            LoadSubData();
            ResetDailyData();

        }

        public void AddGamePlayMinute()
        {
            gameplayMinute++;
            if (gameplayMinute % 5 == 0)
            {
                DataAnalysisMgr.S.ResetEventMap()
                .AddEventParam("recordtime", gameplayMinute)
                .CustomEventDic("p_gametime", DAPE.ThinkingData);
            }
            SetDataDirty();
        }

        public void AddLevelPlayTimes()
        {
            levelPlayTimes++;
            EventSystem.S.Send(EventID.OnAddPlayTimes);
            SetDataDirty();
        }

        public void SaveShowLevel()
        {
            levelUpShowPanel = false;
            SetDataDirty();
        }

        public void AddExp(int exp)
        {
            playerExp += exp;
            if (playerLv >= TDPlayerLevelTable.count)
            {
                return;
            }
            while (playerLv < TDPlayerLevelTable.count && playerExp >= TDPlayerLevelTable.GetData(playerLv + 1).needExp)
            {
                playerExp -= TDPlayerLevelTable.GetData(playerLv + 1).needExp;
                playerLv++;
                EventSystem.S.Send(EventID.OnPlayerLevelChange, playerLv);
                levelUpShowPanel = true;
                DataCustomMgr.S.UpdateStaticEvtPara(DataCustomDefine.PROP_PLAYER_LV, playerLv);
            }
            EventSystem.S.Send(EventID.OnPlayerExpChange, playerExp);
            // EventSystem.S.Send(ConsumerEvt.EvtExp, playerExp);
            SetDataDirty();
        }

        public int GetExpToNextLevel()
        {
            if (playerLv >= TDPlayerLevelTable.count)
            {
                return 0;
            }
            return TDPlayerLevelTable.GetData(playerLv + 1).needExp;
        }

        public bool isOldUser()
        {
            return newVersion <= 80;
        }

        void LoadSubData()
        {
            if (newVersion < 78)//存在老版本用户 自动删档
            {
                newVersion = 78;
                strFirstStartTime = DateTime.Now.ToStringEx();
                strFirstStartDayTime = DateTime.Now.Date.ToStringEx();
                PlayerPrefs.DeleteAll();
                PlayerInfoMgr.ResetAsNew();
                return;
            }


            if (string.IsNullOrEmpty(strFirstStartDayTime))
            {
                strFirstStartDayTime = DateTime.Parse(strFirstStartTime, System.Globalization.CultureInfo.InvariantCulture).Date.ToStringEx();
            }

            if (consumerData == null)
            {
                consumerData = new ConsumerData();
            }
            consumerData.SetDirtyRecorder(PlayerInfoMgr.dataDirtyRecorder);
            consumerData.OnDataLoadFinish();

            if (battleData == null)
            {
                battleData = new BattleData();
            }
            battleData.SetDirtyRecorder(PlayerInfoMgr.dataDirtyRecorder);
            battleData.OnDataLoadFinish();

            if (cardData == null)
            {
                cardData = new CardData();
            }
            cardData.SetDirtyRecorder(PlayerInfoMgr.dataDirtyRecorder);
            cardData.OnDataLoadFinish();

            if (diceSelectionData == null)
            {
                diceSelectionData = new DiceSelectionData();
            }
            diceSelectionData.SetDirtyRecorder(PlayerInfoMgr.dataDirtyRecorder);
            diceSelectionData.OnDataLoadFinish();

            if (timestampData == null)
            {
                timestampData = new GameTimestampData();
            }
            timestampData.SetDirtyRecorder(PlayerInfoMgr.dataDirtyRecorder);
            timestampData.OnDataLoadFinish();

            if (outpostData == null)
            {
                outpostData = new OutpostData();
            }
            outpostData.SetDirtyRecorder(PlayerInfoMgr.dataDirtyRecorder);
            outpostData.OnDataLoadFinish();


            if (shopData == null)
            {
                shopData = new ShopData();
            }
            shopData.SetDirtyRecorder(PlayerInfoMgr.dataDirtyRecorder);
            shopData.OnDataLoadFinish();


            if (signInData == null)
            {
                signInData = new SignInData();
            }
            signInData.SetDirtyRecorder(PlayerInfoMgr.dataDirtyRecorder);
            signInData.OnDataLoadFinish();

            if (passportData == null)
            {
                passportData = new PassportData();
            }
            passportData.SetDirtyRecorder(PlayerInfoMgr.dataDirtyRecorder);
            passportData.OnDataLoadFinish();


            if (taskData == null)
            {
                taskData = new TaskData();
            }
            taskData.SetDirtyRecorder(PlayerInfoMgr.dataDirtyRecorder);
            taskData.OnDataLoadFinish();

            if (gemSourceData == null)
            {
                gemSourceData = new GemSourceData();
            }
            gemSourceData.Init();
            gemSourceData.TickPerMin();

            Timer.S.Post2Scale(count =>
            {
                gemSourceData.TickPerMin();
            }, 60, -1);


            if (GameDefineHelper.TEST_MODE)
            {
                Log.e("作弊模式新增英雄技能");
                for (int i = 0; i < TDHeroTable.dataList.Count; i++)
                {
                    if (TDHeroTable.dataList[i].open)
                        cardData.AddCardPiece(TDHeroTable.dataList[i].id, 10);
                }
                for (int i = 0; i < TDDiceSelectionTable.dataList.Count; i++)
                {
                    if (TDDiceSelectionTable.dataList[i].open)
                        diceSelectionData.AddSelectionPiece(TDDiceSelectionTable.dataList[i].id, 1);
                }
            }

            if (newVersion < 80)
            {
                cardData.Trans2New();
                newVersion = 80;
            }

            if (newVersion <= 81)
            {
                List<int> m_ErrorRewardCount = new List<int>() { 1200, 3100, 6400, 12900, 22700, 38400, 26000, 43400, 29700, 47000 };
                List<int> m_ErrorRewardCount2 = new List<int>() { 100, 120, 140, 160, 180, 200, 220, 240, 260, 280 };

                if (summonTicket > 100)
                {
                    int allNum = 0;
                    if (battleData.GetModeData(GameMode.CoinDungeon).unlockStageId > 1)
                    {
                        int curUnlock = battleData.GetModeData(GameMode.CoinDungeon).unlockStageId;
                        int num = 0;
                        for (int i = 1; i < curUnlock; i++)
                        {
                            num += m_ErrorRewardCount[i - 1];
                        }
                        allNum += num;
                    }

                    if (battleData.GetModeData(GameMode.SpellDungeon).unlockStageId > 1)
                    {
                        int curUnlock = battleData.GetModeData(GameMode.SpellDungeon).unlockStageId;
                        int num = 0;
                        for (int i = 1; i < curUnlock; i++)
                        {
                            num += m_ErrorRewardCount2[i - 1];
                        }
                        allNum += num;
                    }

                    if (allNum >= summonTicket)
                    {
                        UseSummonTicket(summonTicket, ConsumerSourceType.BugDeal);
                    }
                    else
                    {
                        UseSummonTicket(allNum, ConsumerSourceType.BugDeal);
                    }



                    if (allNum > 0)
                    {
                        var stageReward = new List<RewardInfo>();
                        var coinReward = new RewardInfo();
                        coinReward.gameRewardType = GameRewardType.Coin;
                        coinReward.rewardCount = allNum;
                        var diamondReward = new RewardInfo();
                        diamondReward.gameRewardType = GameRewardType.Diamond;
                        diamondReward.rewardCount = 100;
                        stageReward.Add(coinReward);
                        stageReward.Add(diamondReward);

                        PopWindowMgr.S.AddPopWindowAction(() =>
                        {
                            UIMgr.S.OpenTopPanel(UIID.VersionCompensatePanel, null, stageReward);
                        });

                    }


                }
                newVersion = 82;

            }


            SetDataDirty();
        }

        public void SetLastPlayTime(string time)
        {
            if (long.Parse(time) > long.Parse(lastPlayTimeString))
            {
                lastPlayTimeString = time;
                SetDataDirty();
            }
        }

        /// <summary>
        /// 以零点为基准的天数计算
        /// </summary>
        /// <returns></returns>
        public int GetPassDayZero()
        {
            DateTime time = DateTime.Parse(strFirstStartDayTime, System.Globalization.CultureInfo.InvariantCulture);
            var ts = DateTime.Now - time;
            return ts.Days;
        }
        /// <summary>
        /// 以安装时间24小时后为基准的天数计算
        /// </summary>
        /// <returns></returns>
        public int GetPassDay24()
        {
            DateTime time = DateTime.Parse(strFirstStartTime, System.Globalization.CultureInfo.InvariantCulture);
            var ts = DateTime.Now - time;
            return ts.Days;
        }

        // public void SetDay()
        // {
        //     day = DateTime.Now.DayOfYear;
        //     SetDataDirty();
        // }

        // public bool IsNewDay()
        // {
        //     if (day <= 0)
        //     {
        //         var firstDate = DateTime.Parse(strFirstStartTime, System.Globalization.CultureInfo.InvariantCulture);
        //         day = firstDate.DayOfYear;
        //         SetDataDirty();
        //     }
        //     return DateTime.Now.DayOfYear != day;
        // }

        public void ResetDailyData()
        {
            if (GetPassDayZero() > passDay)
            {
                dailyAdCount = 0;
                passDay = GetPassDayZero();
                outpostData.ResetDailyData();
                signInData.ResetDaily();
                shopData.ResetDailyData();
                passportData.ResetDaily();
                battleData.ResetDailyData();
                taskData.ResetDailyData();
                SetDataDirty();
            }
        }
        public void ForceResetDailyData()
        {
#if !UNITY_EDITOR
return;
#endif
            dailyAdCount = 0;
            passDay = GetPassDayZero();
            outpostData.ResetDailyData();
            signInData.ResetDaily();
            shopData.ResetDailyData();
            passportData.ResetDaily();
            battleData.ResetDailyData();
            taskData.ResetDailyData();
            SetDataDirty();
        }

        #region [Ad]
        public void AddPlayAdCount()
        {
            playAdTotalCount++;
            dailyAdCount++;
            SetDataDirty();
        }
        #endregion

        public int diamond => consumerData.GetConsumerCount(ConsumerEnum.Diamond);
        public int coin => consumerData.GetConsumerCount(ConsumerEnum.Coin);
        public int stamina => consumerData.GetConsumerCount(ConsumerEnum.Stamina);
        public int starStone => consumerData.GetConsumerCount(ConsumerEnum.StarStone);
        public int rune => consumerData.GetConsumerCount(ConsumerEnum.Rune);
        public int essence => consumerData.GetConsumerCount(ConsumerEnum.Essence);
        public int summonTicket => consumerData.GetConsumerCount(ConsumerEnum.SummonTicket);
        public int skillTicket => consumerData.GetConsumerCount(ConsumerEnum.SkillTicket);
        public int skillPiece => consumerData.GetConsumerCount(ConsumerEnum.SkillPiece);

        public void AddDiamond(int count, ConsumerSourceType sourceType)
        {
            consumerData.AddConsumers(ConsumerEnum.Diamond, count);
            gemSourceData.AddGemSourceNum(sourceType, count);
            DataCustomMgr.S.UpdateStaticEvtPara(DataCustomDefine.PROP_GEM, diamond);
        }

        public bool UseDiamond(int count, ConsumerSourceType sourceType)
        {
            bool success = consumerData.UseConsumers(ConsumerEnum.Diamond, count);
            gemSourceData.UseGemSourceNum(sourceType, count);
            DataCustomMgr.S.UpdateStaticEvtPara(DataCustomDefine.PROP_GEM, diamond);
            if (success)
            {
                EventSystem.S.Send(EventID.OnTaskCostDiamond, count);
            }
            return success;
        }

        public void AddCoin(int count, ConsumerSourceType sourceType)
        {
            consumerData.AddConsumers(ConsumerEnum.Coin, count);
            gemSourceData.AddCoinSourceNum(sourceType, count);
            DataCustomMgr.S.UpdateStaticEvtPara(DataCustomDefine.PROP_COIN, coin);
        }

        public bool UseCoin(int count, ConsumerSourceType sourceType)
        {
            bool success = consumerData.UseConsumers(ConsumerEnum.Coin, count);
            gemSourceData.UseCoinSourceNum(sourceType, count);
            DataCustomMgr.S.UpdateStaticEvtPara(DataCustomDefine.PROP_COIN, coin);
            return success;
        }
        public void AddStarStone(int count, ConsumerSourceType sourceType)
        {
            consumerData.AddConsumers(ConsumerEnum.StarStone, count);
            gemSourceData.AddEggSourceNum(sourceType, count);
            DataCustomMgr.S.UpdateStaticEvtPara(DataCustomDefine.PROP_STARSTONE, starStone);
        }

        public bool UseStarStone(int count, ConsumerSourceType sourceType)
        {
            bool success = consumerData.UseConsumers(ConsumerEnum.StarStone, count);
            gemSourceData.UseEggSourceNum(sourceType, count);
            DataCustomMgr.S.UpdateStaticEvtPara(DataCustomDefine.PROP_STARSTONE, starStone);
            if (success)
            {
                EventSystem.S.Send(EventID.OnTaskUseEgg, count);
            }
            return success;
        }

        public void AddSummonTicket(int count, ConsumerSourceType sourceType)
        {
            consumerData.AddConsumers(ConsumerEnum.SummonTicket, count);
            gemSourceData.AddSummonTicketSourceNum(sourceType, count);
            DataCustomMgr.S.UpdateStaticEvtPara(DataCustomDefine.PROP_SUMMON_TICKET, summonTicket);
        }

        public bool UseSummonTicket(int count, ConsumerSourceType sourceType)
        {
            bool success = consumerData.UseConsumers(ConsumerEnum.SummonTicket, count);
            gemSourceData.UseSummonTicketSourceNum(sourceType, count);
            DataCustomMgr.S.UpdateStaticEvtPara(DataCustomDefine.PROP_SUMMON_TICKET, summonTicket);
            return success;
        }

        public void AddSkillTicket(int count, ConsumerSourceType sourceType)
        {
            consumerData.AddConsumers(ConsumerEnum.SkillTicket, count);
            gemSourceData.AddSkillTicketSourceNum(sourceType, count);
            DataCustomMgr.S.UpdateStaticEvtPara(DataCustomDefine.PROP_SKILL_TICKET, skillTicket);
        }

        public bool UseSkillTicket(int count, ConsumerSourceType sourceType)
        {
            bool success = consumerData.UseConsumers(ConsumerEnum.SkillTicket, count);
            gemSourceData.UseSkillTicketSourceNum(sourceType, count);
            DataCustomMgr.S.UpdateStaticEvtPara(DataCustomDefine.PROP_SKILL_TICKET, skillTicket);
            return success;
        }
        public void AddSkillPiece(int count, ConsumerSourceType sourceType)
        {
            consumerData.AddConsumers(ConsumerEnum.SkillPiece, count);
            gemSourceData.AddSkillPieceSourceNum(sourceType, count);
            DataCustomMgr.S.UpdateStaticEvtPara(DataCustomDefine.PROP_SKILL_PIECE, skillPiece);
        }

        public bool UseSkillPiece(int count, ConsumerSourceType sourceType)
        {
            bool success = consumerData.UseConsumers(ConsumerEnum.SkillPiece, count);
            gemSourceData.UseSkillPieceSourceNum(sourceType, count);
            DataCustomMgr.S.UpdateStaticEvtPara(DataCustomDefine.PROP_SKILL_PIECE, skillPiece);
            return success;
        }

        public void AddRune(int count, ConsumerSourceType sourceType)
        {
            consumerData.AddConsumers(ConsumerEnum.Rune, count);
            gemSourceData.AddRuneSourceNum(sourceType, count);
            DataCustomMgr.S.UpdateStaticEvtPara(DataCustomDefine.PROP_RUNE, rune);
        }

        public bool UseRune(int count, ConsumerSourceType sourceType)
        {
            bool success = consumerData.UseConsumers(ConsumerEnum.Rune, count);
            gemSourceData.UseRuneSourceNum(sourceType, count);
            DataCustomMgr.S.UpdateStaticEvtPara(DataCustomDefine.PROP_RUNE, rune);
            return success;
        }
        public void AddEssence(int count, ConsumerSourceType sourceType)
        {
            consumerData.AddConsumers(ConsumerEnum.Essence, count);
            gemSourceData.AddEssenceSourceNum(sourceType, count);
            DataCustomMgr.S.UpdateStaticEvtPara(DataCustomDefine.PROP_ESSENCE, essence);
        }

        public bool UseEssence(int count, ConsumerSourceType sourceType)
        {
            bool success = consumerData.UseConsumers(ConsumerEnum.Essence, count);
            gemSourceData.UseEssenceSourceNum(sourceType, count);
            DataCustomMgr.S.UpdateStaticEvtPara(DataCustomDefine.PROP_ESSENCE, essence);
            return success;
        }

        public void AddStamina(int count)
        {
            consumerData.AddConsumers(ConsumerEnum.Stamina, count);
            // gemSourceData.AddStaminaSourceNum(sourceType, count);
            DataCustomMgr.S.UpdateStaticEvtPara(DataCustomDefine.PROP_STAMINA, stamina);
        }

        public bool UseStamina(int count)
        {
            bool success = consumerData.UseConsumers(ConsumerEnum.Stamina, count);
            passportData.AddPassExp(count);
            // gemSourceData.UseStaminaSourceNum(sourceType, count);
            DataCustomMgr.S.UpdateStaticEvtPara(DataCustomDefine.PROP_STAMINA, stamina);
            return success;
        }

    }
}
