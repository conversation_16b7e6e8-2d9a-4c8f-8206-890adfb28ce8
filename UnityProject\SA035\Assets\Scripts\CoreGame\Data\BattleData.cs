using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Qarth;
using System;


namespace GameWish.Game
{
	public class BattleData : DataDirtyHandler
	{
		public int gamemode = 0;
		public GameMode curMode => (GameMode)gamemode;
		public List<ModeData> modeDatas = new List<ModeData>();
		public DungeonData coinDungeonData = new DungeonData();
		public DungeonData spellDungeonData = new DungeonData();
		public DungeonData heroDungeonData = new DungeonData();
		public DungeonData dragonDungeonData = new DungeonData();
		public int killEnemyCount = 0;
		public int levelPlayTimes = 0;

		public int adGameSpdUpTimeRemain = 0;
		public string adRaidCDTimestamp = "0";
		public int dailyFreeRaid = 0;
		public int initHeroId = 1;

		public DungeonData GetDungeonData(GameMode mode)
		{
			switch (mode)
			{
				default:
				case GameMode.CoinDungeon:
					return coinDungeonData;
				case GameMode.SpellDungeon:
					return spellDungeonData;
				case GameMode.HeroPieceDungeon:
					return heroDungeonData;
				case GameMode.DragonDungeon:
					return dragonDungeonData;
			}
		}


		public StageData curStageData(GameMode mode)
		{
			return GetModeData(mode).curStageData;
		}

		public List<StageData> GetStageDataList(GameMode mode)
		{
			return GetModeData(mode).stageDataList;
		}

		public int UnlockStageId(GameMode mode = GameMode.Normal)
		{
			return GetModeData(mode).unlockStageId;
		}

		public int CurStageId(GameMode mode = GameMode.Normal)
		{
			return GetModeData(mode).curStageId;
		}

		public void ChangeGameMode(GameMode mode)
		{
			gamemode = (int)mode;
			SetDataDirty();
			EventSystem.S.Send(EventID.OnGameModeChange, mode);
		}


		public void OnDataLoadFinish()
		{
			if (modeDatas == null || modeDatas.Count == 0)
			{
				modeDatas = new List<ModeData>();
				var modeData = new ModeData();
				modeData.mode = (int)GameMode.Normal;
				modeData.InitWithEmptyData();
				modeDatas.Add(modeData);
			}

			if (coinDungeonData == null)
			{
				coinDungeonData = new DungeonData();
			}
			if (spellDungeonData == null)
			{
				spellDungeonData = new DungeonData();
			}
			if (heroDungeonData == null)
			{
				heroDungeonData = new DungeonData();
			}
			if (dragonDungeonData == null)
			{
				dragonDungeonData = new DungeonData();
			}

			if (string.IsNullOrEmpty(adRaidCDTimestamp))
			{
				adRaidCDTimestamp = "0";
			}

			if (initHeroId == 0)
			{
				initHeroId = 1;
			}

			ChangeGameMode(GameMode.Normal);
			SetDataDirty();
		}

		public void ChangeInitHero(int id)
		{
			if (initHeroId == id) return;
			initHeroId = id;
			EventSystem.S.Send(EventID.OnInitHeroChanged, id);
			SetDataDirty();
		}

		public void AddEnemyKillCount(int num)
		{
			killEnemyCount += num;
			EventSystem.S.Send(EventID.OnBonusProgressUpdate, killEnemyCount);
			SetDataDirty();
		}

		public void AddLevelPlayTimes()
		{
			levelPlayTimes++;
			EventSystem.S.Send(EventID.OnLevelPlayTimesChange, levelPlayTimes);
			SetDataDirty();
		}

		public void CheckLevelPlayTimes()
		{
			if (levelPlayTimes < GameDefineHelper.UNLOCK_CARD_NEED)
			{
				levelPlayTimes = GameDefineHelper.UNLOCK_CARD_NEED;
				EventSystem.S.Send(EventID.OnLevelPlayTimesChange, levelPlayTimes);
				SetDataDirty();
			}
		}

		public ModeData GetModeData(GameMode mode)
		{
			var data = modeDatas.Find(item => item.mode == (int)mode);
			if (data == null)
			{
				data = new ModeData();
				data.mode = (int)mode;
				data.InitWithEmptyData();
				modeDatas.Add(data);
			}
			return data;
		}

		#region  OldMethod
		/// <summary>
		/// 过关
		/// </summary>
		public void AddStage(int wave, GameMode mode)
		{
			var modeData = GetModeData(mode);
			modeData.AddStage(wave);
			SetDataDirty();
		}

		public void SaveWave(int wave, GameMode mode)
		{
			var modeData = GetModeData(mode);
			modeData.SaveWave(wave);
			SetDataDirty();
		}

		public StageData GetStageData(int stage, GameMode mode)
		{
			var modeData = GetModeData(mode);
			return modeData.GetStageData(stage);
		}

		public void GetStageChestReward(int stage, int index, GameMode mode)
		{
			var modeData = GetModeData(mode);
			modeData.GetStageChestReward(stage, index);
			SetDataDirty();
		}
		public void GetStageChestRewardDouble(int stage, int index, GameMode mode)
		{
			GetStageData(stage, mode).GetDouble();
			SetDataDirty();
		}



		public StageChestState IsStageChestCanGet(int stage, int index, GameMode mode)
		{
			var modeData = GetModeData(mode);
			return modeData.IsStageChestCanGet(stage, index);
		}

		public void ChangeSelectStage(int stage, GameMode mode)
		{
			var modeData = GetModeData(mode);
			modeData.ChangeSelectStage(stage);
			SetDataDirty();
		}

		#endregion

		public void SetAdFreeSpdUpTime()
		{
			adGameSpdUpTimeRemain += GameDefineHelper.SPD_UP_AD_CD;
			SetDataDirty();
		}
		public void ReduceAdFreeSpdUpTime()
		{
			if (adGameSpdUpTimeRemain <= 0)
			{
				adGameSpdUpTimeRemain = 0;
			}
			else
			{
				adGameSpdUpTimeRemain--;
			}
			SetDataDirty();
		}
		public void SetAdRaidCDTime()
		{
			adRaidCDTimestamp = CustomExtensions.GetTimeStampAfterSec(CustomExtensions.GetTimeStamp(), GameDefineHelper.COMMON_AD_CD);
			EventSystem.S.Send(EventID.OnRaidCDChanged);
			SetDataDirty();
		}
		public int GetAdRaidCDRemain()
		{
			int sec = (int)CustomExtensions.GetSecToTimestamps(adRaidCDTimestamp);
			return sec;
		}

		public bool CanAdRaid()
		{
			int remain = GetAdRaidCDRemain();
			return remain <= 0 && dailyFreeRaid <= 0;
		}

		public bool HasDailyFreeRaid()
		{
			return dailyFreeRaid > 0;
		}

		public void UseFreeRaid()
		{
			dailyFreeRaid--;
		}

		public void ResetDailyData()
		{
			dailyFreeRaid = 0;
			coinDungeonData.ResetDailyData();
			spellDungeonData.ResetDailyData();
			heroDungeonData.ResetDailyData();
			dragonDungeonData.ResetDailyData();
			SetDataDirty();
		}
	}

	public class StageData
	{
		public int stageId;
		public int maxWave = -1;//改为最大波次了
		public List<bool> chestGetState = new List<bool>();//宝箱领取状态
		public List<int> chestWave = new List<int>();

		public bool getDouble = false;

		public void SetCurMaxWave(int wave)
		{
			if (wave < maxWave) return;
			maxWave = wave;
		}

		public void SetChestGet(int wave, bool get)
		{
			if (!chestWave.Contains(wave))
			{
				chestGetState.Add(false);
				chestWave.Add(wave);
			}
			int index = chestWave.IndexOf(wave);
			chestGetState[index] = get;
			EventSystem.S.Send(EventID.OnStageChestGet, stageId, index);
		}

		public bool IsChestGot(int wave)
		{
			if (!chestWave.Contains(wave))
			{
				chestGetState.Add(false);
				chestWave.Add(wave);
			}
			int index = chestWave.IndexOf(wave);
			return chestGetState[index];
		}

		public void GetDouble()
		{
			getDouble = true;
		}

		public StageChestState GetStageChestState(int wave)
		{
			if (maxWave >= wave)
			{
				if (!IsChestGot(wave))
				{
					return StageChestState.CanGet;
				}
				else
				{
					return StageChestState.Opened;
				}
			}
			else
			{
				return StageChestState.Locked;
			}
		}
	}

	public class ModeData : DataDirtyHandler
	{
		public int mode;
		public int unlockStageId;//已经解锁的最大关卡
		public int curStageId;//当前选中的关卡
		public List<StageData> stageDataList = new List<StageData>();
		public StageData curStageData => stageDataList[curStageId - 1];

		public int curChapterId => curStageId % 7 > 0 ? (curStageId / 7) + 1 : curStageId / 7;

		public void InitWithEmptyData()
		{
			if (stageDataList == null || stageDataList.Count == 0)
			{
				unlockStageId = 1;
				curStageId = 1;
				AddNewStageData(1);
			}
			SetDataDirty();
		}

		/// <summary>
		/// 过关
		/// </summary>
		/// <param name="wave">关卡城堡血量进度</param>
		public void AddStage(int wave)
		{
			curStageData.SetCurMaxWave(wave);
			SetDataDirty();
			if (curStageId < unlockStageId)//不是最大关卡就不解锁新关卡
			{
				return;
			}
			unlockStageId++;
			curStageId++;
			int maxLevel = TDStageTable.GetLevelCount((GameMode)mode);
			if (curStageId > maxLevel || unlockStageId > maxLevel)
			{
				curStageId = maxLevel;
				unlockStageId = maxLevel;
				SetDataDirty();
				return;
			}
			AddNewStageData(unlockStageId);
			EventSystem.S.Send(EventID.OnStageAdd, curStageId);
			RedPointMgr.S.CheckArrowPoint((GameMode)mode);
			DataCustomMgr.S.UpdateStaticEvtPara(mode == 0 ? DataCustomDefine.PROP_STAGE : DataCustomDefine.PROP_STAGE_MODE2, unlockStageId);
		}

		public void SaveWave(int wave)
		{
			curStageData.SetCurMaxWave(wave);
			SetDataDirty();
		}

		public StageData GetStageData(int stage)
		{
			// Log.e(stage);
			if (stage > stageDataList.Count)
			{
				stage = stageDataList.Count;
			}
			var data = stageDataList[stage - 1];
			return data;
		}

		public void GetStageChestReward(int stage, int wave)
		{
			var data = stageDataList[stage - 1];
			data.SetChestGet(wave, true);
			SetDataDirty();
			RedPointMgr.S.CheckArrowPoint((GameMode)mode);
		}


		public StageChestState IsStageChestCanGet(int stage, int wave)
		{
			var data = stageDataList[stage - 1];
			return data.GetStageChestState(wave);
		}

		public void ChangeSelectStage(int stage)
		{
			curStageId = stage;
			EventSystem.S.Send(EventID.OnCurStageChange, curStageId);
			RedPointMgr.S.CheckArrowPoint((GameMode)mode);
		}

		private void AddNewStageData(int id)
		{
			if (id > TDStageTable.GetLevelCount((GameMode)mode))
			{
				return;
			}
			StageData stageData = new StageData();
			stageData.stageId = id;
			//stageData.IsChestGot(2);
			stageDataList.Add(stageData);
			SetDataDirty();
		}
	}


	public class DungeonData : DataDirtyHandler
	{
		public int dailyFreeRaidTimes = 2;
		public int dailyAdRaidTimes = 3;
		public int dailyRaidTimes = 5;
		public int bestRecord = 0;

		public void ResetDailyData()
		{
			dailyFreeRaidTimes = 2;
			dailyAdRaidTimes = 3;
			dailyRaidTimes = 5;
			SetDataDirty();
			RedPointMgr.S.CheckDungeon();
		}

		public void UseFreeRaid()
		{
			dailyFreeRaidTimes--;
			dailyRaidTimes--;
			EventSystem.S.Send(EventID.OnDungeonRaidTimesChange);
			SetDataDirty();
			RedPointMgr.S.CheckDungeon();
		}

		public void UseAdRaid()
		{
			dailyAdRaidTimes--;
			dailyRaidTimes--;
			EventSystem.S.Send(EventID.OnDungeonRaidTimesChange);
			SetDataDirty();
			RedPointMgr.S.CheckDungeon();
		}

		public void SetBestRecord(int value)
		{
			if (value > bestRecord)
			{
				bestRecord = value;
			}
			SetDataDirty();
		}
	}
}