﻿using UnityEngine;
using <PERSON>arth;
using System.Collections.Generic;
using System;


namespace GameWish.Game
{
    public class InputModule : AbstractModule
    {
        private IInputter m_KeyboardInputter;
        private KeyCodeTracker m_KeyCodeTracker;

        public override void OnComLateUpdate(float dt)
        {
            m_KeyboardInputter.LateUpdate();
            m_KeyCodeTracker.LateUpdate();
        }

        protected override void OnComAwake()
        {
            m_KeyCodeTracker = new KeyCodeTracker();
            m_KeyCodeTracker.SetDefaultProcessListener(ShowBackKeydownTips);

            m_KeyboardInputter = new KeyboardInputter();
#if UNITY_EDITOR
            m_KeyboardInputter.RegisterKeyCodeMonitor(KeyCode.F1, null, OnClickF1, null);
            m_KeyboardInputter.RegisterKeyCodeMonitor(KeyCode.F2, null, OnClickF2, null);
            m_KeyboardInputter.RegisterKeyCodeMonitor(KeyCode.F3, null, OnClickF3, null);
            m_KeyboardInputter.RegisterKeyCodeMonitor(KeyCode.F4, null, OnClickF4, null);
            m_KeyboardInputter.RegisterKeyCodeMonitor(KeyCode.F5, null, OnClickF5, null);
            m_KeyboardInputter.RegisterKeyCodeMonitor(KeyCode.F6, null, OnClickF6, null);
            m_KeyboardInputter.RegisterKeyCodeMonitor(KeyCode.F7, null, OnClickF7, null);
            m_KeyboardInputter.RegisterKeyCodeMonitor(KeyCode.F8, null, OnClickF8, null);
#endif
        }

        private void ShowBackKeydownTips()
        {

            //  RedPointMgr.S.UpdateRedPoint(RedPointType.SignIn, true);
        }

        private void OnClickF1()
        {
            //Log.e(GameRewardType.Coin.ToString());
        }

        private void OnClickF2()
        {
            // UIMgr.S.OpenTopPanel(UIID.UnlockFunctionPanel, null, BottomMenuType.Shop);
            var stageData = TDStageTable.GetData(PlayerInfoMgr.data.battleData.UnlockStageId());
            int coin = (int)(stageData.waveDropAllCoin.rewardCount);
            Log.e(coin);
        }



        private void OnClickF3()
        {
            UIMgr.S.OpenPanel(UIID.Giftpack3DayInfoPanel, GameDefineHelper.IAP_PACK_3DAY_2);
        }

        private void OnClickF4()
        {
            PlayerInfoMgr.data.ForceResetDailyData();
        }

        private void OnClickF5()
        {
            PlayerInfoMgr.data.AddCoin(100000000, ConsumerSourceType.Test);
            PlayerInfoMgr.data.AddDiamond(100000, ConsumerSourceType.Test);

            PlayerInfoMgr.data.AddSkillPiece(10000, ConsumerSourceType.Test);
            PlayerInfoMgr.data.AddSkillTicket(10000, ConsumerSourceType.Test);
            PlayerInfoMgr.data.AddSummonTicket(10000, ConsumerSourceType.Test);




        }

        private void OnClickF6()
        {
            int roleId = 40001;
            TDEnemy eData = TDEnemyTable.GetData(roleId);
            var offset = 0;
            var offsetY = 0;
            // Log.e("offsetX  {0}", offset);
            // Log.e("offsetY  {0}", offsetY);
            RoleProperties info = new RoleProperties(true, 1, offset, offsetY);
            //赋值info
            info.atk = eData.commonData.atk;
            info.hp = eData.commonData.hp;

            // Log.e("原{0}  攻击力加成比例 {1}  现攻击力{2}", eData.commonData.atk, m_WaveData.enemyAtkUpRate.RealValue(GameValueType.Float), info.atk);
            // Log.e("原{0} 血量加成比例 {1}  现血量{2}", eData.commonData.hp, m_WaveData.enemyHpUpRate.RealValue(GameValueType.Float), info.hp);
            info.moveSpeed = eData.commonData.moveSpeed;
            info.atkRange = eData.commonData.atkRange;
            info.atkInterval = eData.commonData.atkInterval;
            info.dotAtk = (int)(eData.commonData.dotAtk);
            info.dotInterval = eData.commonData.dotInterval;
            info.dotTime = eData.commonData.dotTime;
            info.searchingRange = eData.commonData.searchingRange;

            var enemy = GameLogicMgr.S.gameLevel.roleCtrl.SpawnEnemy(roleId, info);
        }

        private void OnClickF7()
        {
            GameLogicMgr.S.ForceSummonHero(12);
        }

        private void OnClickF8()
        {
            BuildIAPConfig.BuildConfig();
        }

        private void OnSceneLoadResult(string sceneName, bool result)
        {
            Log.i("SceneLoad:" + sceneName + " " + result);
        }
    }
}
