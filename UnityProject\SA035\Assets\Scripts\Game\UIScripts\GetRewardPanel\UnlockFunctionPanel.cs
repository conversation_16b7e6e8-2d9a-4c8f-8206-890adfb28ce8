using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Qarth;
using UnityEngine.UI;
using TMPro;
using DG.Tweening;



namespace GameWish.Game
{
	public enum GameFunctionType
	{
		Card,
		Artifact,
		Stage,
		Dungeon,
		Shop,

		BoxReward,
		Sign,
		Outpost,
		Passport,

		CoinDungeon,
		SpellDungeon,
		HeroDungeon,
		DragonDungeon,

	}
	public class UnlockFunctionPanel : AbstractPanel
	{
		[SerializeField] private TextMeshProUGUI m_TxtUnlockDesc;
		[SerializeField] private Transform m_TranLock;
		[SerializeField] private Image m_ImgIcon;
		[SerializeField] private Transform m_EffctRoot;
		[SerializeField] private Animator m_Animator;
		private ParticleSystem[] m_LstEff = null;
		private Sequence m_Sequence = null;


		private string m_ShopIcon = "mainpanel_img_shangcheng";
		private string m_CardIcon = "mainpanel_img_bingzhong";
		private string m_SignInIcon = "mainpanel1_qd_icon";
		private string m_OutpostIcon = "mainpanel_AFK Reward_icon";
		private string m_BoxRewardIcon = "mainpanel_img_baoxianglan";
		private string m_PassportIcon = "mainpanel1_txz_icon";
		private string m_CoinDungeonIcon = "dailychallengepanel_Coin_challenge_icon";
		private string m_SpellDungeonIcon = "dailychallengepanel_SummoningStone_challenge_icon";
		private string m_HeroDungeonIcon = "dailychallengepanel_heroFragment_challenge_icon";
		private string m_DragonDungeonIcon = "dailychallengepanel_challengedragon_challenge_icon";

		protected override void OnUIInit()
		{
			base.OnUIInit();
			m_LstEff = m_EffctRoot.GetComponentsInChildren<ParticleSystem>();

		}

		protected override void OnOpen()
		{
			base.OnOpen();
			m_Animator.enabled = false;

		}

		protected override void OnPanelOpen(params object[] args)
		{
			base.OnPanelOpen(args);
			OpenDependPanel(EngineUI.MaskPanel, -1);
			RedPointMgr.S.CheckDungeon();
			GameFunctionType type = (GameFunctionType)args[0];
			EventID eventId = EventID.UnlockFunctionCard;
			m_TxtUnlockDesc.transform.localScale = Vector3.zero;
			Vector3 targetPos = Vector3.zero;
			m_EffctRoot.gameObject.SetActive(false);
			switch (type)
			{
				case GameFunctionType.Card:
				case GameFunctionType.Shop:
					BottomBarPanel bottomBarPanel = UIMgr.S.FindPanel(UIID.BottomBarPanel) as BottomBarPanel;
					BottomBtnItem btnItem = bottomBarPanel.GetBottomBtnItem((BottomMenuType)type);
					targetPos = btnItem.iconTran.position;
					switch (type)
					{
						case GameFunctionType.Card:
							eventId = EventID.UnlockFunctionCard;
							GameRewardHelper.GetSprite(m_CardIcon, spr =>
							{
								m_ImgIcon.sprite = spr;
								m_ImgIcon.SetNativeSize();
							});
							m_TxtUnlockDesc.text = TDLanguageTable.GetFormat("desc_function_unlock", TDLanguageTable.Get("hero_title"));
							break;
						case GameFunctionType.Shop:

							eventId = EventID.UnlockFunctionShop;
							GameRewardHelper.GetSprite(m_ShopIcon, spr =>
							{
								m_ImgIcon.sprite = spr;
								m_ImgIcon.SetNativeSize();
							});
							m_TxtUnlockDesc.text = TDLanguageTable.GetFormat("desc_function_unlock", TDLanguageTable.Get("shop_title"));
							break;
					}
					break;

				case GameFunctionType.CoinDungeon:
				case GameFunctionType.SpellDungeon:
				case GameFunctionType.HeroDungeon:
				case GameFunctionType.DragonDungeon:
					bottomBarPanel = UIMgr.S.FindPanel(UIID.BottomBarPanel) as BottomBarPanel;
					btnItem = bottomBarPanel.GetBottomBtnItem(BottomMenuType.Dungeon);
					targetPos = btnItem.iconTran.position;
					switch (type)
					{
						case GameFunctionType.CoinDungeon:
							eventId = EventID.UnlockFunctionCoinDungeon;
							GameRewardHelper.GetSprite(m_CoinDungeonIcon, spr =>
							{
								m_ImgIcon.sprite = spr;
								m_ImgIcon.SetNativeSize();
							});
							m_TxtUnlockDesc.text = TDLanguageTable.GetFormat("desc_function_unlock", TDLanguageTable.Get("dungeon_1"));
							break;
						case GameFunctionType.SpellDungeon:

							eventId = EventID.UnlockFunctionSpellDungeon;
							GameRewardHelper.GetSprite(m_SpellDungeonIcon, spr =>
							{
								m_ImgIcon.sprite = spr;
								m_ImgIcon.SetNativeSize();
							});
							m_TxtUnlockDesc.text = TDLanguageTable.GetFormat("desc_function_unlock", TDLanguageTable.Get("dungeon_2"));
							break;
						case GameFunctionType.HeroDungeon:

							eventId = EventID.UnlockFunctionHeroDungeon;
							GameRewardHelper.GetSprite(m_HeroDungeonIcon, spr =>
							{
								m_ImgIcon.sprite = spr;
								m_ImgIcon.SetNativeSize();
							});
							m_TxtUnlockDesc.text = TDLanguageTable.GetFormat("desc_function_unlock", TDLanguageTable.Get("dungeon_3"));
							break;

						case GameFunctionType.DragonDungeon:
							eventId = EventID.UnlockFunctionHeroDungeon;
							GameRewardHelper.GetSprite(m_DragonDungeonIcon, spr =>
							{
								m_ImgIcon.sprite = spr;
								m_ImgIcon.SetNativeSize();
							});
							m_TxtUnlockDesc.text = TDLanguageTable.GetFormat("desc_function_unlock", TDLanguageTable.Get("dungeon_4"));
							break;
					}
					break;

				default:
					MainPanel mainPanel = UIMgr.S.FindPanel(UIID.MainPanel) as MainPanel;
					switch (type)
					{
						case GameFunctionType.Sign:
							targetPos = mainPanel.tranSign.position;
							eventId = EventID.UnlockFunctionSign;
							GameRewardHelper.GetSprite(m_SignInIcon, spr =>
							{
								m_ImgIcon.sprite = spr;
								m_ImgIcon.SetNativeSize();
							});
							m_TxtUnlockDesc.text = TDLanguageTable.GetFormat("desc_function_unlock", TDLanguageTable.Get("btn_signin"));
							break;
						case GameFunctionType.BoxReward:
							targetPos = mainPanel.tranBoxReward.position;
							eventId = EventID.UnlockFunctionBoxReward;
							GameRewardHelper.GetSprite(m_BoxRewardIcon, spr =>
							{
								m_ImgIcon.sprite = spr;
								m_ImgIcon.SetNativeSize();
							});
							m_TxtUnlockDesc.text = TDLanguageTable.GetFormat("desc_function_unlock", TDLanguageTable.Get("chest_title"));
							break;
						case GameFunctionType.Outpost:
							targetPos = mainPanel.tranOutpost.position;
							eventId = EventID.UnlockFunctionOutpost;
							GameRewardHelper.GetSprite(m_OutpostIcon, spr =>
							{
								m_ImgIcon.sprite = spr;
								m_ImgIcon.SetNativeSize();
							});
							m_TxtUnlockDesc.text = TDLanguageTable.GetFormat("desc_function_unlock", TDLanguageTable.Get("outpost"));
							break;
						case GameFunctionType.Passport:
							targetPos = mainPanel.tranPassport.position;
							eventId = EventID.UnlockFunctionPassport;
							GameRewardHelper.GetSprite(m_PassportIcon, spr =>
							{
								m_ImgIcon.sprite = spr;
								m_ImgIcon.SetNativeSize();
							});
							m_TxtUnlockDesc.text = TDLanguageTable.GetFormat("desc_function_unlock", TDLanguageTable.Get("btn_passport"));
							break;
					}
					break;

			}

			//开锁动画
			m_Animator.enabled = true;
			this.CallWithDelay(() =>
			{
				m_Sequence?.Kill();
				m_Sequence = DOTween.Sequence().Append(m_ImgIcon.transform.DOScale(2, 0.3f).SetEase(Ease.OutBack))
				.Join(m_TxtUnlockDesc.transform.DOScale(1, 0.3f).SetEase(Ease.OutBack))
				.Insert(0.6f, m_ImgIcon.transform.DOMove(targetPos, 0.5f))
				.Join(m_ImgIcon.transform.DOScale(1, 0.5f))
				.Join(m_TxtUnlockDesc.DOFade(0.5f, 0.5f));
				//图标闪烁特效
				m_EffctRoot.gameObject.SetActive(true);
				m_ImgIcon.material = null;
				foreach (var item in m_LstEff)
				{
					item.Play();
				}
				m_TranLock.gameObject.SetActive(false);
				m_TxtUnlockDesc.gameObject.SetActive(true);
				m_Sequence.OnComplete(() =>
				{
					EventSystem.S.Send(eventId);
					CloseSelfPanel();
				});
			}, 1.2f);



		}

		protected override void OnClose()
		{
			base.OnClose();
			m_Sequence?.Kill();

		}

		void OnClickClose()
		{

			CloseSelfPanel();
		}

	}

}