//=====================================================
// - FileName:      RedPointMgr.cs
// - Author:       Autumn
// - CreateTime:    2023/05/17 14:57:59
// - Email:         <EMAIL>
// - Description:   
// -  (C) Copyright 2022, Seikami,Inc.
// -  All Rights Reserved.
//======================================================
using System;
using System.Collections.Generic;
using GameWish.Game;
using UnityEngine;

namespace Qarth
{
    /// <summary>
    /// 以A.B.C的形式定义红点树,如果C有一个红点，那么B和A都会有红点，会一层层传递出去
    /// 根节点为Root
    /// </summary>
    public partial class RedPointConst
    {
        public const string card = "Root.Card";
        public const string artifact = "Root.Artifact";

        public const string shop = "Root.Shop";
        public const string hero = "Root.Card.Hero";
        public const string spell = "Root.Card.Spell";
        public const string stage = "Root.Stage";
        public const string leftArrow = "Root.Stage.leftArrow";
        public const string rightArrow = "Root.Stage.rightArrow";

        public const string outpost = "Root.Stage.Outpost";
        public const string boxReward = "Root.Stage.BoxReward";
        public const string shopFreeCoin = "Root.Shop.Resource.FreeCoin";
        public const string laba = "Root.Laba";
        public const string sign = "Root.Sign";
        public const string passport = "Root.Passport";
        public const string Giftpack = "Root.Shop.Giftpack";
        public const string ticket = "Root.Shop.Ticket";
        public const string resource = "Root.Shop.Resource";

        public const string shopMonth1 = "Root.Shop.Giftpack.Month1";
        public const string shopMonth2 = "Root.Shop.Giftpack.Month2";
        public const string shopMonth3 = "Root.Shop.Ticket.Month3";
        public const string outpostAcc = "Root.Stage.Outpost.Acc";
        public const string outpostReward = "Root.Stage.Outpost.Get";

        public const string rune = "Root.Rune";
        public const string dungeon = "Root.Dungeon";


    }

    public partial class RedPointMgr : TSingleton<RedPointMgr>
    {

        #region 自定义区域
        public void CheckAllRedPoint()
        {
            CheckShop();
            CheckHero();
            CheckArrowPoint(PlayerInfoMgr.data.battleData.curMode);
            CheckArtifactSlot();
            CheckJewelGem();
            CheckOutpost();
            CheckSign();
            CheckPassport();
            CheckFreeGem();
            CheckMonthCard();
            CheckOutpostGuide();
            CheckRune();
            CheckDungeon();
        }


        private bool m_ShopUnlock;
        public void CheckShop()
        {
            SetNodeCount(RedPointConst.shopFreeCoin, 0);

            m_ShopUnlock = BotToggleHelper.GetUnlock(BottomMenuType.Shop);
            if (!m_ShopUnlock) return;

            bool haveFree = PlayerInfoMgr.data.shopData.dailyCoinRemain > 0 && PlayerInfoMgr.data.shopData.GetFreeCoinAdRemainTime() <= 0;
            if (haveFree)
            {
                SetNodeCount(RedPointConst.shopFreeCoin, 1);
            }
        }

        public void CheckFreeGem()
        {
        }


        private string[] monthName = new string[3] { GameDefineHelper.IAP_PACK_SUPER, GameDefineHelper.IAP_PACK_PREMIUM, GameDefineHelper.IAP_PACK_NOAD };
        private string[] monthRedKey = new string[3] { RedPointConst.shopMonth1, RedPointConst.shopMonth2, RedPointConst.shopMonth3 };
        public void CheckMonthCard()
        {
            if (!m_ShopUnlock) return;
            // pack_super 
            //pack_premium 
            //pack_noad
            for (int i = 0; i < monthName.Length; i++)
            {
                var month = PlayerInfoMgr.data.shopData.GetMonthlyCardData(monthName[i]);
                bool showRed = month.isBuy && !month.dailyGet;
                SetNodeCount(monthRedKey[i], showRed ? 1 : 0);
            }
        }


        public void CheckSign()
        {
            SetNodeCount(RedPointConst.sign, 0);
            if (!PlayerInfoMgr.data.signInData.dailySigned)
            {
                SetNodeCount(RedPointConst.sign, 1);
            }
        }

        public void CheckRune()
        {
            SetNodeCount(RedPointConst.rune, 0);

            //   bool unlock = BotToggleHelper.GetUnlock(BottomMenuType.Artifact);
            //  if (!unlock) return;
            // var lst = PlayerInfoMgr.data.runesData.lstRunes;
            // for (int i = 0; i < lst.Count; i++)
            // {
            //     var m_TDRuneData = TDRuneTable.GetData(lst[i].runesId);
            //     bool preUnlock = PlayerInfoMgr.data.runesData.GetRunesData(m_TDRuneData.preId) == null || PlayerInfoMgr.data.runesData.GetRunesData(m_TDRuneData.preId).isUnlock;
            //     bool consumerEnough = PlayerInfoMgr.data.consumerData.GetConsumerCount((ConsumerEnum)m_TDRuneData.consumeType) >= m_TDRuneData.consumeValue;
            //     bool upgradable = preUnlock && consumerEnough && !lst[i].isUnlock;
            //     if (upgradable)
            //     {
            //         SetNodeCount(RedPointConst.rune, 1);
            //         break;
            //     }
            // }
        }


        public void CheckHero()
        {
            bool unlock = BotToggleHelper.GetUnlock(BottomMenuType.Card);
            if (!unlock) return;

            int count = 0;
            var lst = PlayerInfoMgr.data.cardData.GetUnlockCardDatas();
            for (int x = 0; x < lst.Count; x++)
            {
                int id = lst[x].id;
                var m_Record = PlayerInfoMgr.data.cardData.GetCard(id);
                bool unlocked = !m_Record.isLocked;
                bool upgradable = false;
                //是否可以升级
                if (m_Record.level < TDRoleLevelTable.count)
                {
                    //金币是读正常的
                    int m_CurCoinNeed = TDRoleLevelTable.GetData(m_Record.level + 1).coinCount;
                    bool coinEnough = PlayerInfoMgr.data.coin >= m_CurCoinNeed;

                    upgradable = coinEnough && unlocked;
                }

                if (!upgradable)
                {
                    //判断是否可以进阶
                    bool isStarUp = TDRoleStarUpTable.GetData(m_Record.id, m_Record.starLevel + 1) != null;
                    if (isStarUp)
                    {
                        var starUpData = TDRoleStarUpTable.GetData(m_Record.id, m_Record.starLevel + 1);
                        int pieceNeed = starUpData.cost;
                        bool pieceEnough = m_Record.piece >= pieceNeed;
                        bool coinEnough = true;// PlayerInfoMgr.data.coin >= starUpData.coinCost;
                        upgradable = pieceEnough && coinEnough && unlocked;
                    }
                }


                //判断技能是否可以升级
                var skillItems = new List<TDDiceSelection>();
                for (int i = 0; i < TDDiceSelectionTable.count; i++)
                {
                    if (TDDiceSelectionTable.dataList[i].character == m_Record.id && TDDiceSelectionTable.dataList[i].type != 0
                    && TDDiceSelectionTable.dataList[i].openLevel <= PlayerInfoMgr.data.battleData.UnlockStageId()
                    && (TDDiceSelectionTable.dataList[i].unlockAuto || PlayerInfoMgr.data.diceSelectionData.GetUnlockSelections().Contains(TDDiceSelectionTable.dataList[i].id))
                    && TDDiceSelectionTable.dataList[i].canUpgrade)
                    {
                        skillItems.Add(TDDiceSelectionTable.dataList[i]);
                    }
                }
                for (int i = 0; i < skillItems.Count; i++)
                {

                    var selectionRecord = PlayerInfoMgr.data.diceSelectionData.GetDiceSelectionItemData(skillItems[i].id);
                    int maxLevel = TDDiceSelectionLevelInfoTable.GetSelectionLevel(skillItems[i].id);
                    if (selectionRecord.level >= maxLevel)
                    {
                        continue;
                    }
                    if (m_Record.isLocked)
                    {
                        continue;
                    }
                    var upData = TDDiceSelectionLevelInfoTable.GetData(skillItems[i].id, selectionRecord.level + 1);
                    if (upData != null && upData.piece <= m_Record.selectionPiece)
                    {
                        upgradable = true;
                        if (!GameDefineHelper.TEST_MODE)
                        {
                            PopWindowMgr.S.AddPopWindowAction(() =>
                            {
                                EventSystem.S.Send(EventID.OnGuideCanUpgradeSelection);
                            }, new List<UIID> { UIID.ShopPanel, UIID.MainPanel });
                        }
                        break;
                    }
                }

                if (upgradable)
                {
                    count++;
                }
            }
            SetNodeCount(RedPointConst.hero, count);
        }

        public void CheckArrowPoint(GameMode mode)
        {
            //            Log.e("当前模式 {0}", mode);
            int stage = PlayerInfoMgr.data.battleData.UnlockStageId(mode);//PlayerInfoMgr.data.battleData.CurStageId(mode);
            SetNodeCount(RedPointConst.leftArrow, 0);
            SetNodeCount(RedPointConst.rightArrow, 0);
            SetNodeCount(RedPointConst.boxReward, 0);

            // //Left
            int preCanGetCount = 0;
            int num = mode == GameMode.Normal ? 3 : 1;
            List<int> preStageLst = new List<int>();
            for (int i = stage - 1; i > 0; i--)
            {
                preStageLst.Add(i);
            }


            for (int i = 0; i < TDStageTable.count; i++)
            {
                if (TDStageTable.dataList[i].id > stage)
                {
                    break;
                }
                var infos = TDStageTable.dataList[i].chestRewards;
                foreach (var item in infos)
                {
                    if (item.Key != -1)
                    {
                        var preStageData = PlayerInfoMgr.data.battleData.GetStageData(TDStageTable.dataList[i].id, mode);
                        var state = preStageData.GetStageChestState(item.Key);
                        if (state == StageChestState.CanGet)
                        {
                            preCanGetCount++;
                        }
                    }
                }
            }


            SetNodeCount(RedPointConst.boxReward, preCanGetCount);


            // for (int i = 0; i < preStageLst.Count; i++)
            // {
            //     var preStageData = PlayerInfoMgr.data.battleData.GetStageData(preStageLst[i], mode);

            //     for (int x = 0; x < num; x++)
            //     {
            //         var state = preStageData.GetStageChestState(x);
            //         if (state == StageChestState.CanGet)
            //         {
            //             preCanGetCount++;
            //         }
            //     }
            // }
            // SetNodeCount(RedPointConst.leftArrow, preCanGetCount);

            // //right
            // int nextCanGetCount = 0;

            // var lst = PlayerInfoMgr.data.battleData.GetStageDataList(mode);
            // List<int> nextStageLst = new List<int>();

            // for (int i = stage + 1; i <= lst.Count; i++)
            // {
            //     nextStageLst.Add(i);
            // }

            // for (int i = 0; i < nextStageLst.Count; i++)
            // {
            //     var nextStageData = PlayerInfoMgr.data.battleData.GetStageData(nextStageLst[i], mode);

            //     for (int x = 0; x < num; x++)
            //     {
            //         var state = nextStageData.GetStageChestState(x);
            //         if (state == StageChestState.CanGet)
            //         {
            //             nextCanGetCount++;
            //         }
            //     }
            // }

            // SetNodeCount(RedPointConst.rightArrow, nextCanGetCount);
        }

        public void CheckArtifactSlot()
        {
        }

        public void CheckJewelGem()
        {
        }
        public void CheckDungeon()
        {
            SetNodeCount(RedPointConst.dungeon, 0);
            bool unlock = BotToggleHelper.GetUnlock(BottomMenuType.Dungeon);
            if (!unlock)
            {
                return;
            }
            int nodeCount = 0;
            var dungeonRecord1 = PlayerInfoMgr.data.battleData.GetDungeonData(GameMode.CoinDungeon);
            if (BotToggleHelper.GetDungeonCoinUnlock() && dungeonRecord1.dailyRaidTimes > 0)
            {
                nodeCount++;
            }
            var dungeonRecord2 = PlayerInfoMgr.data.battleData.GetDungeonData(GameMode.SpellDungeon);
            if (BotToggleHelper.GetDungeonSpellUnlock() && dungeonRecord2.dailyRaidTimes > 0)
            {
                nodeCount++;
            }
            var dungeonRecord3 = PlayerInfoMgr.data.battleData.GetDungeonData(GameMode.HeroPieceDungeon);
            if (BotToggleHelper.GetDungeonHeroUnlock() && dungeonRecord3.dailyRaidTimes > 0)
            {
                nodeCount++;
            }
            var dungeonRecord4 = PlayerInfoMgr.data.battleData.GetDungeonData(GameMode.DragonDungeon);
            if (BotToggleHelper.GetDungeonDragonUnlock() && dungeonRecord4.dailyRaidTimes > 0)
            {
                nodeCount++;
            }


            SetNodeCount(RedPointConst.dungeon, nodeCount);

        }

        public void CheckOutpostGuide()
        {
            bool unlock = BotToggleHelper.GetOutpostUnlock();
            if (!unlock)
            {
                CloseOutpostGuide();
                return;
            }
            CloseOutpostGuide();
            int accCount = PlayerInfoMgr.data.outpostData.dailyAdStaminaChange;
            if (accCount > 0)
            {
                SetNodeCount(RedPointConst.outpostAcc, 1);
            }
            //  Log.e("accCount {0}    redAcc count {1}", accCount, GetNode(RedPointConst.outpostAcc).redPointNum);
        }

        public void CloseOutpostGuide()
        {
            SetNodeCount(RedPointConst.outpostAcc, 0);
            // Log.e("redAcc count {0}", GetNode(RedPointConst.outpostAcc).redPointNum);
        }

        public void CheckOutpost()
        {
            bool unlock = BotToggleHelper.GetOutpostUnlock();
            if (!unlock)
            {
                SetNodeCount(RedPointConst.outpost, 0);
                SetNodeCount(RedPointConst.outpostReward, 0);
                SetNodeCount(RedPointConst.outpostAcc, 0);
                return;
            }

            int leftSec = (int)CustomExtensions.GetSecFromTimestamps(PlayerInfoMgr.data.outpostData.GetOutpostStartTimestamp());
            if (leftSec <= 0)
            {
                SetNodeCount(RedPointConst.outpostReward, 0);
                return;
            }
            int hour = leftSec / 3600;
            int min = (leftSec % 3600) / 60;
            int step = Mathf.Clamp(hour, 0, 6);
            if (step >= 1)
            {
                SetNodeCount(RedPointConst.outpostReward, 1);
            }
            else
            {
                SetNodeCount(RedPointConst.outpostReward, 0);
            }

            //  Log.e(" redReward count {0}", GetNode(RedPointConst.outpostReward).redPointNum);
        }

        public void CheckPassport()
        {

            SetNodeCount(RedPointConst.passport, 0);

            bool unlock = BotToggleHelper.GetUnlock(BottomMenuType.Shop);
            if (!unlock) return;

            var lst = TDPassportTable.dataList;
            for (int i = 1; i < lst.Count + 1; i++)
            {
                for (int j = 1; j < 4; j++)
                {
                    bool hasGet = PlayerInfoMgr.data.passportData.HasGetReward(i, j);
                    bool buyPass = PlayerInfoMgr.data.passportData.HasBuyPass(j) && j != 2;
                    if (!hasGet && buyPass && i <= PlayerInfoMgr.data.passportData.level)
                    {
                        SetNodeCount(RedPointConst.passport, 1);
                    }
                }
            }
        }



        #endregion
    }

}

