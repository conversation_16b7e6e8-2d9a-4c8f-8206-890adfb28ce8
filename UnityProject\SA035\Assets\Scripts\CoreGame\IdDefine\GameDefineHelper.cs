using System.Collections;
using System.Collections.Generic;
using UnityEngine;


namespace GameWish.Game
{
	public class GameDefineHelper
	{
		public const int BATTLE_ROLE_COUNT = 3;
		public const int BATTLE_SPELL_COUNT = 2;
		public const int UNLOCK_CARD_NEED = 5;
		public const int BATTLE_TAROT_COUNT = 8;
		public const int STAGE_STAMINA = 5;
		public const int STAMINA_MAX = 30;
		public const float BULLET_LIFETIME = 1.2f;
		public const int GEM_CHEST_NORMAL_COST = 80;
		public const int GEM_CHEST_NORMAL_COST_10 = 750;
		public const int GEM_CHEST_ADVANCE_COST = 400;
		public const int GEM_CHEST_ADVANCE_COST_10 = 3750;
		public const int SUMMON_ROLE_COST = 300;
		public const int SUMMON_ROLE_COST_10 = 2900;
		public const int SUMMON_SKILL_COST = 100;
		public const int SUMMON_SKILL_COST_10 = 950;
		public const int SUMMON_DAILY_TIMES = 10;
		public const int GEM_CHEST_NORMAL_AD_CD = 6 * 60 * 60;
		public const int GEM_CHEST_ADVANCE_AD_CD = 24 * 60 * 60;
		public const int COMMON_AD_CD = 120;
		public const int SPD_UP_AD_CD = 180;
		public const int DAILY_AD_COIN = 3;

		public const int OUTPOST_LEVEL = 3;
		public const int BOXREWARD_LEVEL = 1;
		public const int SIGNIN_LEVEL = 3;
		public const int RUNE_LEVEL = 1;
		public const int FEVER_LEVEL = 5;
		public const int SHOP_LEVEL = 5;
		public const int CARD_LEVEL = 1;
		public const int LABA_LEVEL = 5;
		public const int DUNGEON_COIN_LEVEL = 6;
		public const int DUNGEON_SPELL_LEVEL = 9;
		public const int DUNGEON_HERO_LEVEL = 12;
		public const int DUNGEON_DRAGON_LEVEL = 10;
		public const int POP_IAP_LEVEL = 4;
		public const int TASK_UNLOCK = 1;
		public const int PASSPORT_UNLOCK = 5;
		public const int DAILY_TASK_COUNT = 5;
		public const int MAX_WATER = 50;
		public const int DRAW_CARD_UNLOCK = 6;
		public const float DRAGON_SPAWN_INTERVAL = 2f;
		public static int[] DICE_SKILL_INDEX = new int[6] { 0, 0, 0, 0, 0, 0 };
		public static int[] DICE_SKILL_INDEX2 = new int[6] { 0, 0, 0, 1, 1, 1 };
		public static int[] DICE_SKILL_INDEX3 = new int[6] { 0, 0, 0, 1, 1, 2 };
		public static int[] GetDiceSkillIndex(int starLevel)
		{
			if (starLevel < 1)
			{
				return DICE_SKILL_INDEX;
			}
			else if (starLevel < 2)
			{
				return DICE_SKILL_INDEX2;
			}
			else
			{
				return DICE_SKILL_INDEX3;
			}
		}
		public static int[] DICE_INIT_NUM = new int[6] { 1, 1, 1, 1, 1, 1 };

		public static float ORIGIN_SPD_A = 1f;
		public static float ORIGIN_SPD_B = 1.2f;

		public static float GetOriginSpd()
		{
			return ABMgr.S.GetGroup(ABTestEnum.GameSpeedTest) == 0 ? ORIGIN_SPD_A : ORIGIN_SPD_B;
		}


		public const string IAP_PACK_NOAD = "pack_noad";
		public const string IAP_PACK_SUPER = "pack_super";
		public const string IAP_PACK_PREMIUM = "pack_premium";
		public const string IAP_PACK_NEWBIE = "pack_newbie";
		public const string IAP_PACK_3DAY_2 = "pack_newbie";
		public const string IAP_PACK_OUTPOST = "pack_outpost";
		public const string IAP_PACK_VIP = "pack_vip";
		public const string IAP_PACK_3DAY_1 = "pack_first";
		public const bool TEST_MODE = false;


		public static List<int> HERO_LEVEL_UNLOCK_ABILITY_LST = new List<int>() { 2, 6, 10, 14, 18, 22, 26, 30 };
		public const int HERO_LEVEL_UNLOCK_ABILITY_COUNT = 8;

		public static int GetDungeonUnlockLevel(GameMode mode)
		{
			switch (mode)
			{
				default:
				case GameMode.CoinDungeon:
					return DUNGEON_COIN_LEVEL;
				case GameMode.SpellDungeon:
					return DUNGEON_SPELL_LEVEL;
				case GameMode.HeroPieceDungeon:
					return DUNGEON_HERO_LEVEL;
				case GameMode.DragonDungeon:
					return DUNGEON_DRAGON_LEVEL;
			}
		}

		public static int GetBaseHp(int level, int origin, int factor)
		{
			int value = Mathf.RoundToInt(origin * Mathf.Pow((factor * 0.01f), level - 1));
			if (level >= 2)
			{
				int beforeValue = GetBaseHp(level - 1, origin, factor);
				if (value <= beforeValue)
				{
					value = beforeValue + 1;
				}
			}
			return value;
		}

		public static int GetHpProgress(float remainRate)
		{
			if (remainRate < 0.5f)
			{
				return 0;
			}
			else if (remainRate < 1f)
			{
				return 1;
			}
			else
			{
				return 2;
			}
		}



		public static int GetDailyGem(int day)
		{
			return 10 + day * 5;
		}

		public static int GetDailyCoin(int day)
		{
			return 100 + day * 50;
		}

		private static List<int> m_LstBossId = new List<int>() { 1301, 1303, 1302 };
		private static List<float> m_LstBossSlideScale = new List<float>() { 0.5f, 0.5f, 0.3f };
		private static List<float> m_LstBossSliderOffset = new List<float>() { 3, 3, 1.5f };
		public static int GetBossIndex(int roleId)
		{
			int index = m_LstBossId.FindIndex(x => x == roleId) + 1;
			index = Mathf.Clamp(index, 1, 3);
			return index;
		}

		public static float GetBossSliderScale(int roleId)
		{
			return m_LstBossSlideScale[m_LstBossId.FindIndex(x => x == roleId)];
		}

		public static Vector3 GetBossSliderOffset(int roleId)
		{
			return new Vector3(0, m_LstBossSliderOffset[m_LstBossId.FindIndex(x => x == roleId)], 0);
		}

		public static float GetStageEnemyAtkUpRate(int stageId)
		{
			var stageData = TDStageTable.GetData(stageId, PlayerInfoMgr.data.battleData.curMode);
			return stageData.waveInfos[0].enemyAtkUpRate.RealValue(GameValueType.Float);
		}


	}

}