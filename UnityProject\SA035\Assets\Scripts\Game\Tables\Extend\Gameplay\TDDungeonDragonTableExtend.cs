using UnityEngine;
using System;
using System.IO;
using System.Collections;
using System.Collections.Generic;
using Qarth;

namespace GameWish.Game
{
    public partial class TDDungeonDragonTable
    {
        static void CompleteRowAdd(TDDungeonDragon tdData)
        {

        }

        public static List<TDDungeonDragon> GetDragonDatas(int dragonId = 1)
        {
            List<TDDungeonDragon> tar = new List<TDDungeonDragon>();
            for (int i = 0; i < dataList.Count; i++)
            {
                if (dataList[i].dragonId == dragonId)
                {
                    tar.Add(dataList[i]);
                }
            }
            return tar;
        }


        public static List<RewardInfo> GetAvailableRewards(List<int> lstKill)
        {
            var lst = GetDragonDatas();
            List<RewardInfo> infos = new List<RewardInfo>();
            for (int i = 0; i < lst.Count; i++)
            {
                if (!lstKill.Contains(lst[i].id))
                {
                    continue;
                }
                if (lst[i].chestType < 0)
                {
                    continue;
                }
                for (int x = 0; x < lst[i].rewardInfos.Count; x++)
                {
                    RewardInfo it = new RewardInfo();
                    it.gameRewardType = lst[i].rewardInfos[x].gameRewardType;
                    it.rewardCount = lst[i].rewardInfos[x].rewardCount;
                    it.rewardParam = lst[i].rewardInfos[x].rewardParam;
                    it.rewardQuality = lst[i].rewardInfos[x].rewardQuality;
                    if (it.rewardCount == 0)
                    {
                        continue;
                    }
                    var same = infos.Find(item => (item.gameRewardType == it.gameRewardType && item.rewardParam == it.rewardParam));
                    if (same == null)
                    {
                        infos.Add(it);
                    }
                    else
                    {
                        same.rewardCount += it.rewardCount;
                    }
                }
            }
            return infos;

        }
    }
}