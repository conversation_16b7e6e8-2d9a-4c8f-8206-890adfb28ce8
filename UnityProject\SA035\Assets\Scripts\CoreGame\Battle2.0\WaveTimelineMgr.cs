using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Qarth;
using UnityEngine.Playables;
using System;
using Game.Timeline;
using UnityEngine.Timeline;


namespace GameWish.Game
{
	public class WaveTimelineMgr : TSingleton<WaveTimelineMgr>
	{
		protected Dictionary<int, Dictionary<int, PlayableAsset>> m_DicLevelWaveAsset = new();
		public Dictionary<int, Dictionary<int, PlayableAsset>> dicLevelWaveAsset => m_DicLevelWaveAsset;


		public void LoadLevelConfig(int levelId, GameMode mode, Action loadCallback = null)
		{
			if (m_DicLevelWaveAsset.ContainsKey(levelId))
			{
				loadCallback?.Invoke();
				return;
			}
			var levelTableData = TDStageTable.GetData(levelId, mode);
			if (levelTableData == null)
			{
				Log.e("无效关卡");
				loadCallback?.Invoke();
				return;
			}
			Dictionary<int, PlayableAsset> m_DicWaveAsset = new();
			int finalLevelId = TDStageTable.GetLevelId(levelId, mode);
			for (int i = 0; i < levelTableData.waveInfos.Count; i++)
			{
				int waveId = i + 1;
				string assetName = $"level_{finalLevelId}_wave_{waveId}";
				AddressableResMgr.S.LoadAssetAsyncByName<PlayableAsset>(assetName, (asset, success) =>
				{
					if (success)
					{
						if (!m_DicWaveAsset.ContainsKey(waveId))
						{
							m_DicWaveAsset.Add(waveId, asset);
						}
					}
					if (waveId == levelTableData.waveInfos.Count)
					{
						if (!m_DicLevelWaveAsset.ContainsKey(levelId))
						{
							m_DicLevelWaveAsset.Add(levelId, m_DicWaveAsset);
						}
						loadCallback?.Invoke();
					}
				});
			}

		}

		public List<int> GetLevelEnmies(int levelId)
		{
			Dictionary<int, PlayableAsset> levelWaveAsset = m_DicLevelWaveAsset[levelId];
			List<int> enmies = new();
			foreach (var item in levelWaveAsset)
			{
				var asset = item.Value as TimelineAsset;

				foreach (var track in asset.GetOutputTracks())
				{
					foreach (TimelineClip clip in track.GetClips())
					{
						if (clip.asset is EnemySpawnClipAsset spawnClip)
						{
							foreach (var generateSetting in spawnClip.GenerateSettings)
							{
								foreach (var enemyId in generateSetting.id)
								{
									if (!enmies.Contains(enemyId))
									{
										enmies.Add(enemyId);
									}
								}
							}
						}
					}

				}

			}
			return enmies;

		}

		public List<int> GetWaveEnmieIds(int levelId, int waveId)
		{
			Dictionary<int, PlayableAsset> levelWaveAsset = m_DicLevelWaveAsset[levelId];
			List<int> enmies = new();
			foreach (var item in levelWaveAsset)
			{
				if (item.Key == waveId)
				{
					var asset = item.Value as TimelineAsset;

					foreach (var track in asset.GetOutputTracks())
					{
						foreach (TimelineClip clip in track.GetClips())
						{
							if (clip.asset is EnemySpawnClipAsset spawnClip)
							{
								foreach (var generateSetting in spawnClip.GenerateSettings)
								{
									foreach (var enemyId in generateSetting.id)
									{
										for (int i = 0; i < generateSetting.count; i++)
										{
											enmies.Add(enemyId);
										}
									}
								}
							}
						}

					}
				}


			}
			return enmies;
		}

		public void Release()
		{
			foreach (var item in m_DicLevelWaveAsset)
			{
				foreach (var item2 in item.Value)
				{
					AddressableResMgr.S.ReleaseCachedAssetByName($"level_{item.Key}_wave_{item2.Key}");
				}
			}
			m_DicLevelWaveAsset.Clear();
		}
	}
}