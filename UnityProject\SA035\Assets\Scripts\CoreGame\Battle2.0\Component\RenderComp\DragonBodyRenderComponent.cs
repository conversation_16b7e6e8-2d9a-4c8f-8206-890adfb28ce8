using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Qarth;
using DG.Tweening;
using TMPro;


namespace GameWish.Game
{
	public class DragonBodyRenderComponent : RoleDoubleRenderComponent
	{
		public BodyChestShow bodyChest;
		private bool m_CanShowBlood;
		private DragonBody m_Body;
		public void ShowChest(int type, bool isShow)
		{
			if (bodyChest == null) return;
			HideAllChest();
			m_CanShowBlood = isShow;
			if (type < 0) return;
			bodyChest.lstChest[type].SetActive(isShow);
		}

		private void HideAllChest()
		{
			if (bodyChest == null) return;
			for (int i = 0; i < bodyChest.lstChest.Count; i++)
			{
				bodyChest.lstChest[i].SetActive(false);
			}
			bodyChest.txtBlood.text = "";
		}

		private void ShowBlood(int key, params object[] param)
		{
			if (bodyChest == null) return;
			if (!m_CanShowBlood) return;
			int groupId = (int)param[0];
			int blood = (int)param[1];
			if (groupId != m_Body.group.groupId) return;
			if (blood < 0)
			{
				bodyChest.txtBlood.text = "0";
				HideAllChest();
				return;
			}
			bodyChest.txtBlood.text = blood.ToString();
		}
		public override void InitComponent(Entity owner)
		{
			m_Owner = owner;
			m_Disposed = false;
			active = true;
			m_Role = owner as GameRole;
			m_Body = m_Role as DragonBody;
			string prefabName = m_Role.commonData.prefab;
			RegisterEvent(EventID.OnDragonBloodUpdate, ShowBlood);
			//加载预制体
			GameLogicMgr.S.gameLevel.roleCtrl.GetRoleModel(m_Role.instanceId, m_Role.commonData.id, prefabName, go =>
			{
				if (GameLogicMgr.S.gameLevel == null)
				{
					return;
				}
				gameObject = go;
				transform = go.transform;
				transform.gameObject.layer = LayerMask.NameToLayer(m_Role.isEnemy ? m_LayerEnemy : m_LayerFriend);
				m_RenderTran = transform.Find(m_RenderName);
				m_FrontGo = m_RenderTran.Find(m_FrontName).gameObject;
				bodyChest = transform.GetComponentInChildren<BodyChestShow>();

				m_OriginScale = Vector3.one * m_Role.commonData.scale;
				m_RenderTran.localScale = m_OriginScale;
				m_FrontSkeletonAnimation = null;// m_FrontGo.GetComponentInChildren<SkeletonAnimation>();
												//m_FrontSkeletonAnimation.Initialize(false);
				m_FrontRenderer = m_FrontGo.GetComponentInChildren<MeshRenderer>();
				skeletonAnimation = m_FrontSkeletonAnimation;
				m_Renderer = m_FrontRenderer;
				m_Role.PlayAnim("idle", true, null);
				EventSystem.S.Send(EventID.OnRoleRenderLoadFinish, m_Role.instanceId);
				var data = TDDungeonDragonTable.GetData(m_Body.group.tableId);
				ShowChest(data.chestType, m_Body.indexInGroup == 2);
				EventSystem.S.Send(EventID.OnDragonBloodUpdate, m_Body.group.groupId, m_Body.group.commonBlood);
			});

		}


		public override void DoHurtAnim()
		{
			if (m_TweenHurt != null || m_IsStealth)
			{
				return;
			}
			//变色
			m_TweenHurt = DOVirtual.DelayedCall(0.15f, () =>
			{
				m_TweenHurt = null;
				//还原颜色
			});
		}

		public override void PlayHitAnim()
		{

		}


	}

}