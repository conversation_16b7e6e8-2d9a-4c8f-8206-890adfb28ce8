using System.Collections;
using System.Collections.Generic;
using Qarth;
using UnityEngine;


namespace GameWish.Game
{
	public struct DragonMoveInfo
	{
		public Vector3 position;
		public DungeonDragonMoveComponent.MoveDirection direction;
		public int pathIndex;

		public DragonMoveInfo(Vector3 pos, DungeonDragonMoveComponent.MoveDirection dir, int index)
		{
			position = pos;
			direction = dir;
			pathIndex = index;
		}
	}

	public class DungeonDragonMoveComponent : RoleMoveComponent
	{
		public enum MoveDirection
		{
			Left = 0,
			Right,
			Down,
		}
		private float m_XLeft = -3.5f;
		private float m_XRight = 3.5f;
		private float m_YInterval = 2f;
		private List<MoveDirection> m_PathDirection = new List<MoveDirection>()
		{
			MoveDirection.Right,//10
			MoveDirection.Down,
			MoveDirection.Left,//8
			MoveDirection.Down,
			MoveDirection.Right,//6
			MoveDirection.Down,
			MoveDirection.Left,//4
			MoveDirection.Down,
			MoveDirection.Right,//2
			MoveDirection.Down,
			MoveDirection.Left,//0
			MoveDirection.Down,
			MoveDirection.Right,//-2
			MoveDirection.Down,
			MoveDirection.Left,//-4
			MoveDirection.Down
		};

		//right->down->left->down->right->

		private MoveDirection m_Direction = MoveDirection.Right;
		protected override void OnLoadFinish(int key, params object[] param)
		{
			if (m_Role.instanceId == (int)param[0])
			{
				var pos = m_Role.forcePos;
				m_Role.roleRenderComponent.transform.position = new Vector3(-2.5f, 10f, 0);
				m_Role.roleRenderComponent.transform.SetZ(-0.2f);
				canMove = true;
			}
		}
		private int m_CurPathIndex = 0;
		public int curPathIndex => m_CurPathIndex;
		float moveDownDis = 0f;
		private float m_DeltaTime = Time.deltaTime;
		float m_MoveTime = 0f;
		DragonMoveInfo moveInfo;
		public override void Tick(float deltaTime)
		{
			if (deltaTime > 0)
			{
				m_DeltaTime = deltaTime;
			}
			if (!canMove) return;
			if (m_Role.roleRenderComponent.transform == null) return;
			//S型路线移动，先往左下角移动，x到达边界，就改为反方向右下角，以此类推
			if (m_CurPathIndex >= m_PathDirection.Count - 1)
			{
				//到终点了 游戏结束
				EventSystem.S.Send(EventID.OnBattleEnd, false);
				return;
			}
			// m_Direction = m_PathDirection[m_CurPathIndex];
			// switch (m_Direction)
			// {
			// 	case MoveDirection.Left:
			// 		m_Role.roleRenderComponent.transform.position += (Vector3)(Vector2.left * deltaTime * m_Role.runtimeData.moveSpeed);
			// 		m_Role.roleRenderComponent.ChangeTurn(true, true);
			// 		break;
			// 	case MoveDirection.Right:
			// 		m_Role.roleRenderComponent.ChangeTurn(true, false);
			// 		m_Role.roleRenderComponent.transform.position += (Vector3)(Vector2.right * deltaTime * m_Role.runtimeData.moveSpeed);
			// 		break;
			// 	case MoveDirection.Down:
			// 		moveDownDis += (deltaTime * m_Role.runtimeData.moveSpeed);
			// 		m_Role.roleRenderComponent.transform.position += (Vector3)(Vector2.down * deltaTime * m_Role.runtimeData.moveSpeed);
			// 		break;
			// }

			// //往下走
			// if (m_Direction == MoveDirection.Right && m_Role.roleRenderComponent.transform.position.x >= m_XRight)
			// {
			// 	m_CurPathIndex++;
			// 	//Log.e(m_CurPathIndex);
			// 	moveDownDis = 0f;
			// }
			// else if (m_Direction == MoveDirection.Left && m_Role.roleRenderComponent.transform.position.x <= m_XLeft)
			// {
			// 	m_CurPathIndex++;
			// 	//Log.e(m_CurPathIndex);
			// 	moveDownDis = 0f;
			// }
			// else if (m_Direction == MoveDirection.Down && moveDownDis >= m_YInterval)
			// {
			// 	m_CurPathIndex++;
			// 	//Log.e(m_CurPathIndex);
			// }
			m_MoveTime += deltaTime;
			moveInfo = GetPositionAtTime(m_MoveTime);
			m_Role.roleRenderComponent.transform.position = moveInfo.position;
			m_Direction = moveInfo.direction;
			m_CurPathIndex = moveInfo.pathIndex;
		}
		private float m_SpawnDragonInterval = GameDefineHelper.DRAGON_SPAWN_INTERVAL;
		// public void ForceChangePos(DragonBody lastBody)
		// {
		// 	if (lastBody == null) return;
		// 	if (lastBody.bodyMove == null || lastBody.bodyMove.curPathIndex > m_PathDirection.Count - 1)
		// 	{
		// 		return;
		// 	}

		// 	int lastMovePathIndex = lastBody.bodyMove.curPathIndex;
		// 	var direction = m_PathDirection[lastMovePathIndex];
		// 	var dir = Vector2.left;
		// 	switch (direction)
		// 	{
		// 		case MoveDirection.Left:
		// 			dir = Vector2.left;
		// 			break;
		// 		case MoveDirection.Right:
		// 			dir = Vector2.right;
		// 			break;
		// 		case MoveDirection.Down:
		// 			dir = Vector2.down;
		// 			break;
		// 	}
		// 	if (lastBody.roleRenderComponent == null) return;
		// 	if (lastBody.roleRenderComponent.transform == null) return;
		// 	if (m_Role.roleRenderComponent == null) return;
		// 	if (m_Role.roleRenderComponent.transform == null) return;
		// 	float moveDis = (dir * m_SpawnDragonInterval * m_DeltaTime * lastBody.runtimeData.moveSpeed).magnitude;
		// 	var targetPos = lastBody.roleRenderComponent.transform.position + (Vector3)(dir * m_SpawnDragonInterval * m_DeltaTime * lastBody.runtimeData.moveSpeed);
		// 	float outDis = 0;
		// 	float firstDis = moveDis;
		// 	//往下走
		// 	if (direction == MoveDirection.Right && targetPos.x >= m_XRight)
		// 	{
		// 		lastMovePathIndex++;
		// 		//先移动到边界
		// 		outDis = targetPos.x - m_XRight;
		// 		firstDis = moveDis - outDis;
		// 	}
		// 	else if (direction == MoveDirection.Left && targetPos.x <= m_XLeft)
		// 	{
		// 		lastMovePathIndex++;
		// 		outDis = m_XLeft - targetPos.x;
		// 		firstDis = moveDis - outDis;
		// 	}
		// 	else if (direction == MoveDirection.Down && targetPos.y - lastBody.roleRenderComponent.transform.position.y >= m_YInterval)
		// 	{
		// 		lastMovePathIndex++;
		// 		outDis = targetPos.y - lastBody.roleRenderComponent.transform.position.y - m_YInterval;
		// 		firstDis = moveDis - outDis;
		// 	}
		// 	m_Role.roleRenderComponent.transform.position = lastBody.roleRenderComponent.transform.position + (Vector3)(dir * firstDis);

		// 	direction = m_PathDirection[lastMovePathIndex];
		// 	switch (direction)
		// 	{
		// 		case MoveDirection.Left:
		// 			dir = Vector2.left;
		// 			break;
		// 		case MoveDirection.Right:
		// 			dir = Vector2.right;
		// 			break;
		// 		case MoveDirection.Down:
		// 			dir = Vector2.down;
		// 			break;
		// 	}
		// 	//检查是否要更换index和方向
		// 	m_Role.roleRenderComponent.transform.position += (Vector3)(dir * outDis);
		// 	m_Direction = direction;
		// 	m_CurPathIndex = lastMovePathIndex;
		// }

		/// <summary>
		/// 通过时间间隔强制改变位置（优化版本）
		/// </summary>
		/// <param name="interval">时间间隔倍数</param>
		public void ForceChangePos(int interval)
		{
			float time = interval * m_SpawnDragonInterval;
			var posInfo = GetPositionAtTime(time);
			m_Role.roleRenderComponent.transform.position = posInfo.position;
			m_Direction = posInfo.direction;
			m_CurPathIndex = posInfo.pathIndex;
			m_MoveTime = time;
		}

		/// <summary>
		/// 根据经过时间获取轨迹上的位置信息（优化版本）
		/// 轨迹数学模型：S型蛇形路径
		///
		/// 数学公式总结：
		/// 1. 水平段长度：horizontalLength = |m_XRight - m_XLeft| = 7.0
		/// 2. 垂直段长度：verticalLength = m_YInterval = 2.0
		/// 3. 完整周期长度：cycleLength = horizontalLength + verticalLength = 9.0
		/// 4. 周期数：cycleIndex = floor(totalDistance / cycleLength)
		/// 5. 周期内距离：distanceInCycle = totalDistance % cycleLength
		///
		/// 位置计算：
		/// - 如果 distanceInCycle <= horizontalLength：在水平移动段
		///   x = startX + direction * distanceInCycle
		///   y = startY - cycleIndex * verticalLength
		/// - 如果 distanceInCycle > horizontalLength：在垂直移动段
		///   x = 边界位置
		///   y = startY - cycleIndex * verticalLength - (distanceInCycle - horizontalLength)
		/// </summary>
		/// <param name="elapsedTime">经过的时间</param>
		/// <param name="speed">移动速度，-1表示使用默认速度</param>
		/// <returns>位置信息</returns>
		public DragonMoveInfo GetPositionAtTime(float elapsedTime, float speed = -1f)
		{
			// 起点固定
			Vector3 startPos = new(-2.5f, 10f, 0f);
			if (elapsedTime <= 0f)
			{
				return new DragonMoveInfo(startPos, MoveDirection.Right, 0);
			}

			// 获取移动速度
			float moveSpeed = speed > 0f ? speed : (m_Role?.runtimeData?.moveSpeed ?? 1f);
			float totalDistance = moveSpeed * elapsedTime;

			// 轨迹参数
			float horizontalLength = m_XRight - m_XLeft; // 7.0
			float verticalLength = m_YInterval; // 2.0
			float cycleLength = horizontalLength + verticalLength; // 9.0

			// 计算当前在第几个完整周期
			int completeCycles = Mathf.FloorToInt(totalDistance / cycleLength);
			float distanceInCurrentCycle = totalDistance % cycleLength;

			// 计算Y坐标（每个周期下降一个垂直间隔）
			float currentY = startPos.y - completeCycles * verticalLength;

			// 判断当前周期内的位置和方向
			bool isMovingRight = completeCycles % 2 == 0; // 偶数周期向右，奇数周期向左
			float currentX;
			MoveDirection currentDirection;
			int pathIndex;

			if (distanceInCurrentCycle <= horizontalLength)
			{
				// 在水平移动段
				if (isMovingRight)
				{
					currentX = m_XLeft + distanceInCurrentCycle;
					currentDirection = MoveDirection.Right;
				}
				else
				{
					currentX = m_XRight - distanceInCurrentCycle;
					currentDirection = MoveDirection.Left;
				}
				pathIndex = completeCycles * 2; // 每个周期包含2个路径段（水平+垂直）
			}
			else
			{
				// 在垂直移动段
				float verticalProgress = distanceInCurrentCycle - horizontalLength;
				currentX = isMovingRight ? m_XRight : m_XLeft;
				currentY -= verticalProgress;
				currentDirection = MoveDirection.Down;
				pathIndex = completeCycles * 2 + 1;
			}

			// 边界检查，确保不超出路径范围
			pathIndex = Mathf.Min(pathIndex, m_PathDirection.Count - 1);

			Vector3 finalPosition = new(currentX, currentY, startPos.z);
			return new DragonMoveInfo(finalPosition, currentDirection, pathIndex);
		}

		public override void HitBack(float backDis, System.Action onComplete = null)
		{
			//免疫击退效果
		}

		public override void Knockback(float backDis)
		{

		}

	}

}